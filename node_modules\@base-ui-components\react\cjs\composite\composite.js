"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.VERTICAL_KEYS_WITH_EXTRA_KEYS = exports.VERTICAL_KEYS = exports.SHIFT = exports.MODIFIER_KEYS = exports.META = exports.HORIZONTAL_KEYS_WITH_EXTRA_KEYS = exports.HORIZONTAL_KEYS = exports.HOME = exports.END = exports.CONTROL = exports.COMPOSITE_KEYS = exports.ARROW_UP = exports.ARROW_RIGHT = exports.ARROW_LEFT = exports.ARROW_KEYS = exports.ARROW_DOWN = exports.ALT = exports.ALL_KEYS = void 0;
Object.defineProperty(exports, "createGridCellMap", {
  enumerable: true,
  get: function () {
    return _utils.createGridCellMap;
  }
});
Object.defineProperty(exports, "findNonDisabledListIndex", {
  enumerable: true,
  get: function () {
    return _utils.findNonDisabledListIndex;
  }
});
Object.defineProperty(exports, "getGridCellIndexOfCorner", {
  enumerable: true,
  get: function () {
    return _utils.getGridCellIndexOfCorner;
  }
});
Object.defineProperty(exports, "getGridCellIndices", {
  enumerable: true,
  get: function () {
    return _utils.getGridCellIndices;
  }
});
Object.defineProperty(exports, "getGridNavigatedIndex", {
  enumerable: true,
  get: function () {
    return _utils.getGridNavigatedIndex;
  }
});
Object.defineProperty(exports, "getMaxListIndex", {
  enumerable: true,
  get: function () {
    return _utils.getMaxListIndex;
  }
});
Object.defineProperty(exports, "getMinListIndex", {
  enumerable: true,
  get: function () {
    return _utils.getMinListIndex;
  }
});
Object.defineProperty(exports, "isIndexOutOfListBounds", {
  enumerable: true,
  get: function () {
    return _utils.isIndexOutOfListBounds;
  }
});
Object.defineProperty(exports, "isListIndexDisabled", {
  enumerable: true,
  get: function () {
    return _utils.isListIndexDisabled;
  }
});
exports.isNativeInput = isNativeInput;
exports.scrollIntoViewIfNeeded = scrollIntoViewIfNeeded;
Object.defineProperty(exports, "stopEvent", {
  enumerable: true,
  get: function () {
    return _utils.stopEvent;
  }
});
var _utils = require("../floating-ui-react/utils");
const ARROW_UP = exports.ARROW_UP = 'ArrowUp';
const ARROW_DOWN = exports.ARROW_DOWN = 'ArrowDown';
const ARROW_LEFT = exports.ARROW_LEFT = 'ArrowLeft';
const ARROW_RIGHT = exports.ARROW_RIGHT = 'ArrowRight';
const HOME = exports.HOME = 'Home';
const END = exports.END = 'End';
const HORIZONTAL_KEYS = exports.HORIZONTAL_KEYS = new Set([ARROW_LEFT, ARROW_RIGHT]);
const HORIZONTAL_KEYS_WITH_EXTRA_KEYS = exports.HORIZONTAL_KEYS_WITH_EXTRA_KEYS = new Set([ARROW_LEFT, ARROW_RIGHT, HOME, END]);
const VERTICAL_KEYS = exports.VERTICAL_KEYS = new Set([ARROW_UP, ARROW_DOWN]);
const VERTICAL_KEYS_WITH_EXTRA_KEYS = exports.VERTICAL_KEYS_WITH_EXTRA_KEYS = new Set([ARROW_UP, ARROW_DOWN, HOME, END]);
const ARROW_KEYS = exports.ARROW_KEYS = new Set([...HORIZONTAL_KEYS, ...VERTICAL_KEYS]);
const ALL_KEYS = exports.ALL_KEYS = new Set([...ARROW_KEYS, HOME, END]);
const COMPOSITE_KEYS = exports.COMPOSITE_KEYS = new Set([ARROW_UP, ARROW_DOWN, ARROW_LEFT, ARROW_RIGHT, HOME, END]);
const SHIFT = exports.SHIFT = 'Shift';
const CONTROL = exports.CONTROL = 'Control';
const ALT = exports.ALT = 'Alt';
const META = exports.META = 'Meta';
const MODIFIER_KEYS = exports.MODIFIER_KEYS = new Set([SHIFT, CONTROL, ALT, META]);
function isNativeInput(element) {
  if (element instanceof HTMLInputElement && element.selectionStart != null) {
    return true;
  }
  if (element instanceof HTMLTextAreaElement) {
    return true;
  }
  return false;
}
function scrollIntoViewIfNeeded(scrollContainer, element, direction, orientation) {
  if (!scrollContainer || !element) {
    return;
  }
  let targetX = scrollContainer.scrollLeft;
  let targetY = scrollContainer.scrollTop;
  const isOverflowingX = scrollContainer.clientWidth < scrollContainer.scrollWidth;
  const isOverflowingY = scrollContainer.clientHeight < scrollContainer.scrollHeight;
  if (isOverflowingX && orientation !== 'vertical') {
    const elementOffsetLeft = getOffset(scrollContainer, element, 'left');
    const containerStyles = getStyles(element);
    const elementStyles = getStyles(element);
    if (direction === 'ltr') {
      if (elementOffsetLeft + element.offsetWidth + elementStyles.scrollMarginRight > scrollContainer.scrollLeft + scrollContainer.clientWidth - containerStyles.scrollPaddingRight) {
        // overflow to the right, scroll to align right edges
        targetX = elementOffsetLeft + element.offsetWidth + elementStyles.scrollMarginRight - scrollContainer.clientWidth + containerStyles.scrollPaddingRight;
      } else if (elementOffsetLeft - elementStyles.scrollMarginLeft < scrollContainer.scrollLeft + containerStyles.scrollPaddingLeft) {
        // overflow to the left, scroll to align left edges
        targetX = elementOffsetLeft - elementStyles.scrollMarginLeft - containerStyles.scrollPaddingLeft;
      }
    }
    if (direction === 'rtl') {
      if (elementOffsetLeft - elementStyles.scrollMarginRight < scrollContainer.scrollLeft + containerStyles.scrollPaddingLeft) {
        // overflow to the left, scroll to align left edges
        targetX = elementOffsetLeft - elementStyles.scrollMarginLeft - containerStyles.scrollPaddingLeft;
      } else if (elementOffsetLeft + element.offsetWidth + elementStyles.scrollMarginRight > scrollContainer.scrollLeft + scrollContainer.clientWidth - containerStyles.scrollPaddingRight) {
        // overflow to the right, scroll to align right edges
        targetX = elementOffsetLeft + element.offsetWidth + elementStyles.scrollMarginRight - scrollContainer.clientWidth + containerStyles.scrollPaddingRight;
      }
    }
  }
  if (isOverflowingY && orientation !== 'horizontal') {
    const elementOffsetTop = getOffset(scrollContainer, element, 'top');
    const containerStyles = getStyles(element);
    const elementStyles = getStyles(element);
    if (elementOffsetTop - elementStyles.scrollMarginTop < scrollContainer.scrollTop + containerStyles.scrollPaddingTop) {
      // overflow upwards, align top edges
      targetY = elementOffsetTop - elementStyles.scrollMarginTop - containerStyles.scrollPaddingTop;
    } else if (elementOffsetTop + element.offsetHeight + elementStyles.scrollMarginBottom > scrollContainer.scrollTop + scrollContainer.clientHeight - containerStyles.scrollPaddingBottom) {
      // overflow downwards, align bottom edges
      targetY = elementOffsetTop + element.offsetHeight + elementStyles.scrollMarginBottom - scrollContainer.clientHeight + containerStyles.scrollPaddingBottom;
    }
  }
  scrollContainer.scrollTo({
    left: targetX,
    top: targetY,
    behavior: 'auto'
  });
}
function getOffset(ancestor, element, side) {
  const propName = side === 'left' ? 'offsetLeft' : 'offsetTop';
  let result = 0;
  while (element.offsetParent) {
    result += element[propName];
    if (element.offsetParent === ancestor) {
      break;
    }
    element = element.offsetParent;
  }
  return result;
}
function getStyles(element) {
  const styles = getComputedStyle(element);
  return {
    scrollMarginTop: parseFloat(styles.scrollMarginTop) || 0,
    scrollMarginRight: parseFloat(styles.scrollMarginRight) || 0,
    scrollMarginBottom: parseFloat(styles.scrollMarginBottom) || 0,
    scrollMarginLeft: parseFloat(styles.scrollMarginLeft) || 0,
    scrollPaddingTop: parseFloat(styles.scrollPaddingTop) || 0,
    scrollPaddingRight: parseFloat(styles.scrollPaddingRight) || 0,
    scrollPaddingBottom: parseFloat(styles.scrollPaddingBottom) || 0,
    scrollPaddingLeft: parseFloat(styles.scrollPaddingLeft) || 0
  };
}