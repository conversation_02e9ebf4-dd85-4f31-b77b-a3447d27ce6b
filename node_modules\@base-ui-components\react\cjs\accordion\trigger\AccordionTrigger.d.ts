import * as React from 'react';
import { BaseUIComponentProps } from "../../utils/types.js";
import type { AccordionItem } from "../item/AccordionItem.js";
/**
 * A button that opens and closes the corresponding panel.
 * Renders a `<button>` element.
 *
 * Documentation: [Base UI Accordion](https://base-ui.com/react/components/accordion)
 */
export declare const AccordionTrigger: React.ForwardRefExoticComponent<AccordionTrigger.Props & React.RefAttributes<Element>>;
export declare namespace AccordionTrigger {
  interface Props extends BaseUIComponentProps<'button', AccordionItem.State> {
    /**
     * Whether the component renders a native `<button>` element when replacing it
     * via the `render` prop.
     * Set to `false` if the rendered element is not a button (e.g. `<div>`).
     * @default true
     */
    nativeButton?: boolean;
  }
}