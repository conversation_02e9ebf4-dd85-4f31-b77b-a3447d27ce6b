import { add, alter, create, drop, get, set } from "ronin";

export default () => [
  create.model({
    slug: "teacher",
    pluralSlug: "teachers",
    fields: {
      userId: { required: true, target: "user", unique: true, type: "link" },
      bio: { type: "string" },
      isIndependent: { required: true, defaultValue: false, type: "boolean" },
      isVerified: { required: true, defaultValue: false, type: "boolean" },
      createdAt: { required: true, type: "date" },
      updatedAt: { required: true, type: "date" },
    },
  }),
  create.model({
    slug: "school",
    pluralSlug: "schools",
    fields: {
      name: { required: true, type: "string" },
      domain: { unique: true, type: "string" },
      address: { type: "string" },
      phone: { type: "string" },
      placeId: { type: "string" },
      type: { type: "string" },
      district: { type: "string" },
      studentCount: { type: "number" },
      teacherCount: { type: "number" },
      isActive: { required: true, defaultValue: true, type: "boolean" },
      createdAt: { required: true, type: "date" },
      updatedAt: { required: true, type: "date" },
    },
  }),
  create.model({
    slug: "subject",
    pluralSlug: "subjects",
    fields: {
      name: { required: true, unique: true, type: "string" },
      code: { unique: true, type: "string" },
      description: { type: "string" },
      isActive: { required: true, defaultValue: true, type: "boolean" },
      createdAt: { required: true, type: "date" },
    },
  }),
  create.model({
    slug: "class",
    pluralSlug: "classes",
    fields: {
      name: { required: true, type: "string" },
      description: { type: "string" },
      teacherId: { required: true, target: "teacher", type: "link" },
      schoolId: { target: "school", type: "link" },
      subjectId: { target: "subject", type: "link" },
      gradeLevel: { type: "string" },
      maxCapacity: { type: "number" },
      currentEnrollment: { defaultValue: 0, type: "number" },
      isActive: { required: true, defaultValue: true, type: "boolean" },
      startDate: { type: "date" },
      endDate: { type: "date" },
      createdAt: { required: true, type: "date" },
      updatedAt: { required: true, type: "date" },
    },
  }),
  create.model({
    slug: "organization",
    pluralSlug: "organizations",
    fields: {
      name: { required: true, type: "string" },
      slug: { required: true, unique: true, type: "string" },
      logo: { type: "string" },
      metadata: { type: "string" },
      createdAt: { required: true, type: "date" },
      updatedAt: { required: true, type: "date" },
    },
  }),
  create.model({
    slug: "invitation",
    pluralSlug: "invitations",
    fields: {
      organizationId: { required: true, target: "organization", type: "link" },
      email: { required: true, type: "string" },
      role: { required: true, type: "string" },
      status: { required: true, defaultValue: "pending", type: "string" },
      expiresAt: { required: true, type: "date" },
      inviterId: { required: true, target: "user", type: "link" },
      createdAt: { required: true, type: "date" },
      updatedAt: { required: true, type: "date" },
    },
  }),
  create.model({
    slug: "member",
    pluralSlug: "members",
    fields: {
      userId: { required: true, target: "user", type: "link" },
      organizationId: { required: true, target: "organization", type: "link" },
      role: { required: true, type: "string" },
      createdAt: { required: true, type: "date" },
    },
  }),
  create.model({
    slug: "otp",
    pluralSlug: "otps",
    fields: {
      identifier: { required: true, type: "string" },
      value: { required: true, type: "string" },
      expiresAt: { required: true, type: "date" },
      type: { required: true, type: "string" },
      attempts: { required: true, defaultValue: 0, type: "number" },
      createdAt: { required: true, type: "date" },
    },
  }),
  create.model({
    slug: "schoolAdmin",
    pluralSlug: "schoolAdmins",
    fields: {
      userId: { required: true, target: "user", unique: true, type: "link" },
      schoolId: { required: true, target: "school", type: "link" },
      createdAt: { required: true, type: "date" },
      updatedAt: { required: true, type: "date" },
    },
  }),
  create.model({
    slug: "student",
    pluralSlug: "students",
    fields: {
      userId: { required: true, target: "user", unique: true, type: "link" },
      grade: { type: "string" },
      createdAt: { required: true, type: "date" },
      updatedAt: { required: true, type: "date" },
    },
  }),
  create.model({
    slug: "studentClass",
    pluralSlug: "studentClasses",
    fields: {
      studentId: { required: true, target: "student", type: "link" },
      classId: { required: true, target: "class", type: "link" },
      enrolledAt: { required: true, type: "date" },
      status: { required: true, defaultValue: "active", type: "string" },
      finalGrade: { type: "string" },
      completedAt: { type: "date" },
    },
  }),
  create.model({
    slug: "studentTeacher",
    pluralSlug: "studentTeachers",
    fields: {
      studentId: { required: true, target: "student", type: "link" },
      teacherId: { required: true, target: "teacher", type: "link" },
      assignedAt: { required: true, type: "date" },
      status: { required: true, defaultValue: "active", type: "string" },
    },
  }),
  create.model({
    slug: "teacherSchool",
    pluralSlug: "teacherSchools",
    fields: {
      teacherId: { required: true, target: "teacher", type: "link" },
      schoolId: { required: true, target: "school", type: "link" },
      department: { type: "string" },
      status: { required: true, defaultValue: "active", type: "string" },
      invitedBy: { target: "user", type: "link" },
      invitedAt: { type: "date" },
      joinedAt: { type: "date" },
    },
  }),
  create.model({
    slug: "teacherSubject",
    pluralSlug: "teacherSubjects",
    fields: {
      teacherId: { required: true, target: "teacher", type: "link" },
      subjectId: { required: true, target: "subject", type: "link" },
      gradeLevel: { type: "string" },
      createdAt: { required: true, type: "date" },
    },
  }),
  create.model({
    slug: "userRole",
    pluralSlug: "userRoles",
    fields: {
      userId: { required: true, target: "user", unique: true, type: "link" },
      role: { required: true, type: "string" },
      roleId: { required: true, type: "string" },
      createdAt: { required: true, type: "date" },
      updatedAt: { required: true, type: "date" },
    },
  }),
  create.model({
    slug: "waitlist",
    pluralSlug: "waitlists",
    fields: {
      email: { required: true, unique: true, type: "string" },
      name: { required: true, type: "string" },
      userType: { required: true, type: "string" },
      schoolName: { type: "string" },
      schoolAddress: { type: "string" },
      schoolPlaceId: { type: "string" },
      schoolType: { type: "string" },
      schoolDistrict: { type: "string" },
      estimatedStudentCount: { type: "number" },
      estimatedTeacherCount: { type: "number" },
      isApproved: { required: true, defaultValue: false, type: "boolean" },
      approvedAt: { type: "date" },
      approvedBy: { target: "user", type: "link" },
      createdAt: { required: true, type: "date" },
    },
  }),
  create.model({
    slug: "RONIN_TEMP_user",
    fields: {
      email: { required: true, unique: true, type: "string" },
      emailVerified: { required: true, defaultValue: false, type: "boolean" },
      image: { type: "blob" },
      name: { required: true, type: "string" },
      username: { unique: true, type: "string" },
      displayUsername: { type: "string" },
      slug: { required: true, unique: true, type: "string" },
      isActive: { required: true, defaultValue: true, type: "boolean" },
      createdAt: { required: true, type: "date" },
      updatedAt: { required: true, type: "date" },
    },
    idPrefix: "use",
  }),
  add.RONIN_TEMP_users.with(() => get.users({ selecting: ["email", "emailVerified", "image", "name"] })),
  drop.model("user"),
  alter.model("RONIN_TEMP_user").to({ slug: "user", name: "User", pluralName: "Users" }),
  create.model({
    slug: "RONIN_TEMP_user",
    fields: {
      email: { required: false, unique: true, type: "string" },
      emailVerified: { required: false, defaultValue: false, type: "boolean" },
      image: { type: "blob", required: false },
      name: { required: false, type: "string" },
      username: { unique: true, type: "string", required: false },
      displayUsername: { type: "string", required: false },
      slug: { required: false, unique: true, type: "string" },
      isActive: { required: false, defaultValue: true, type: "boolean" },
      createdAt: { required: false, type: "date" },
      updatedAt: { required: false, type: "date" },
    },
    idPrefix: "use",
  }),
  add.RONIN_TEMP_users.with(() => get.users({ selecting: ["email", "emailVerified", "image", "name"] })),
  set.RONIN_TEMP_users({ with: { emailVerified: { being: null } }, to: { emailVerified: true } }),
  alter.model("RONIN_TEMP_users").alter.field("emailVerified").to({ required: true }),
  drop.model("user"),
  alter.model("RONIN_TEMP_user").to({ slug: "user", name: "User", pluralName: "Users" }),
  alter.model("session").create.field({ slug: "activeOrganizationId", type: "string" }),
];
