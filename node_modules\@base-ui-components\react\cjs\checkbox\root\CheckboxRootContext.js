"use strict";
'use client';

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.CheckboxRootContext = void 0;
exports.useCheckboxRootContext = useCheckboxRootContext;
var React = _interopRequireWildcard(require("react"));
const CheckboxRootContext = exports.CheckboxRootContext = /*#__PURE__*/React.createContext(undefined);
if (process.env.NODE_ENV !== "production") CheckboxRootContext.displayName = "CheckboxRootContext";
function useCheckboxRootContext() {
  const context = React.useContext(CheckboxRootContext);
  if (context === undefined) {
    throw new Error('Base UI: CheckboxRootContext is missing. Checkbox parts must be placed within <Checkbox.Root>.');
  }
  return context;
}