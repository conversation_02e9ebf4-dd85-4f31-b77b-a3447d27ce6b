import * as React from 'react';
import { type DialogOpenChangeReason, useDialogRoot } from "./useDialogRoot.js";
/**
 * Groups all parts of the dialog.
 * Doesn’t render its own HTML element.
 *
 * Documentation: [Base UI Dialog](https://base-ui.com/react/components/dialog)
 */
export declare const DialogRoot: React.FC<DialogRoot.Props>;
export declare namespace DialogRoot {
  interface Props extends useDialogRoot.SharedParameters {
    children?: React.ReactNode;
  }
  type Actions = useDialogRoot.Actions;
  type OpenChangeReason = DialogOpenChangeReason;
}