'use strict';

const organization = require('../../shared/better-auth.B7irTavj.cjs');
require('better-call');
require('zod');
require('../../shared/better-auth.b0MLzqQX.cjs');
require('../../cookies/index.cjs');
require('../../shared/better-auth.ANpbi45u.cjs');
require('../../shared/better-auth.C1hdVENX.cjs');
require('../../shared/better-auth.DiSjtgs9.cjs');
require('@better-auth/utils/base64');
require('@better-auth/utils/hmac');
require('../../shared/better-auth.D3mtHEZg.cjs');
require('../../shared/better-auth.C-R0J0n1.cjs');
require('../../social-providers/index.cjs');
require('@better-fetch/fetch');
require('jose');
require('../../shared/better-auth.BLV8UOn8.cjs');
require('@better-auth/utils/hash');
require('@noble/ciphers/chacha');
require('@noble/ciphers/utils');
require('@noble/ciphers/webcrypto');
require('@noble/hashes/scrypt');
require('@better-auth/utils');
require('@better-auth/utils/hex');
require('@noble/hashes/utils');
require('../../shared/better-auth.CYeOI8C-.cjs');
require('@better-auth/utils/random');
require('../../shared/better-auth.GpOOav9x.cjs');
require('../../shared/better-auth.QbbyHMYf.cjs');
require('../../shared/better-auth.Bg6iw3ig.cjs');
require('../../shared/better-auth.C5H9XEzZ.cjs');
require('defu');
require('../../shared/better-auth.BMYo0QR-.cjs');
require('jose/errors');
require('@better-auth/utils/binary');
require('../../shared/better-auth.DhsGZ30Q.cjs');
require('./access/index.cjs');
require('../access/index.cjs');



exports.organization = organization.organization;
exports.parseRoles = organization.parseRoles;
