import * as React from 'react';
import { FloatingRootContext } from "../../floating-ui-react/index.js";
import { type TransitionStatus } from "../../utils/useTransitionStatus.js";
import { type InteractionType } from "../../utils/useEnhancedClickHandler.js";
import type { RequiredExcept, HTMLProps } from "../../utils/types.js";
import { type BaseOpenChangeReason } from "../../utils/translateOpenChangeReason.js";
export type DialogOpenChangeReason = BaseOpenChangeReason | 'close-press';
export declare function useDialogRoot(params: useDialogRoot.Parameters): useDialogRoot.ReturnValue;
export declare namespace useDialogRoot {
  interface SharedParameters {
    /**
     * Whether the dialog is currently open.
     */
    open?: boolean;
    /**
     * Whether the dialog is initially open.
     *
     * To render a controlled dialog, use the `open` prop instead.
     * @default false
     */
    defaultOpen?: boolean;
    /**
     * Determines if the dialog enters a modal state when open.
     * - `true`: user interaction is limited to just the dialog: focus is trapped, document page scroll is locked, and pointer interactions on outside elements are disabled.
     * - `false`: user interaction with the rest of the document is allowed.
     * - `'trap-focus'`: focus is trapped inside the dialog, but document page scroll is not locked and pointer interactions outside of it remain enabled.
     * @default true
     */
    modal?: boolean | 'trap-focus';
    /**
     * Event handler called when the dialog is opened or closed.
     * @type (open: boolean, event?: Event, reason?: Dialog.Root.OpenChangeReason) => void
     */
    onOpenChange?: (open: boolean, event: Event | undefined, reason: DialogOpenChangeReason | undefined) => void;
    /**
     * Event handler called after any animations complete when the dialog is opened or closed.
     */
    onOpenChangeComplete?: (open: boolean) => void;
    /**
     * Determines whether the dialog should close on outside clicks.
     * @default true
     */
    dismissible?: boolean;
    /**
     * A ref to imperative actions.
     * - `unmount`: When specified, the dialog will not be unmounted when closed.
     * Instead, the `unmount` function must be called to unmount the dialog manually.
     * Useful when the dialog's animation is controlled by an external library.
     */
    actionsRef?: React.RefObject<{
      unmount: () => void;
    }>;
  }
  interface Parameters extends RequiredExcept<SharedParameters, 'open' | 'onOpenChange' | 'onOpenChangeComplete' | 'actionsRef'> {
    /**
     * Callback to invoke when a nested dialog is opened.
     */
    onNestedDialogOpen?: (ownChildrenCount: number) => void;
    /**
     * Callback to invoke when a nested dialog is closed.
     */
    onNestedDialogClose?: () => void;
    /**
     * A ref to imperative actions.
     */
    actionsRef?: React.RefObject<Actions>;
  }
  interface ReturnValue {
    /**
     * The id of the description element associated with the dialog.
     */
    descriptionElementId: string | undefined;
    /**
     * Whether the dialog enters a modal state when open.
     */
    modal: boolean | 'trap-focus';
    /**
     * Number of nested dialogs that are currently open.
     */
    nestedOpenDialogCount: number;
    /**
     * Callback to invoke when a nested dialog is closed.
     */
    onNestedDialogClose?: () => void;
    /**
     * Callback to invoke when a nested dialog is opened.
     */
    onNestedDialogOpen?: (ownChildrenCount: number) => void;
    /**
     * Event handler called when the dialog is opened or closed.
     */
    setOpen: (open: boolean, event: Event | undefined, reason: DialogOpenChangeReason | undefined) => void;
    /**
     * Whether the dialog is currently open.
     */
    open: boolean;
    /**
     * Determines what triggered the dialog to open.
     */
    openMethod: InteractionType | null;
    /**
     * Callback to set the id of the description element associated with the dialog.
     */
    setDescriptionElementId: (elementId: string | undefined) => void;
    /**
     * Callback to set the id of the title element.
     */
    setTitleElementId: (elementId: string | undefined) => void;
    /**
     * The id of the title element associated with the dialog.
     */
    titleElementId: string | undefined;
    /**
     * Determines if the dialog should be mounted.
     */
    mounted: boolean;
    /**
     * The transition status of the dialog.
     */
    transitionStatus: TransitionStatus;
    /**
     * Resolver for the Trigger element's props.
     */
    triggerProps: HTMLProps;
    /**
     * Resolver for the Popup element's props.
     */
    getPopupProps: (externalProps?: HTMLProps) => HTMLProps;
    /**
     * Callback to register the Trigger element DOM node.
     */
    setTriggerElement: React.Dispatch<React.SetStateAction<Element | null>>;
    /**
     * Callback to register the Popup element DOM node.
     */
    setPopupElement: React.Dispatch<React.SetStateAction<HTMLElement | null>>;
    /**
     * The ref to the Popup element.
     */
    popupRef: React.RefObject<HTMLElement | null>;
    /**
     * A ref to the backdrop element.
     */
    backdropRef: React.RefObject<HTMLDivElement | null>;
    /**
     * A ref to the internal backdrop element.
     */
    internalBackdropRef: React.RefObject<HTMLDivElement | null>;
    /**
     * The Floating UI root context.
     */
    floatingRootContext: FloatingRootContext;
  }
  interface Actions {
    unmount: () => void;
  }
}