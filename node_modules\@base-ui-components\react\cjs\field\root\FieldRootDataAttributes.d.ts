export declare enum FieldRootDataAttributes {
  /**
   * Present when the field is disabled.
   */
  disabled = "data-disabled",
  /**
   * Present when the field has been touched.
   */
  touched = "data-touched",
  /**
   * Present when the field's value has changed.
   */
  dirty = "data-dirty",
  /**
   * Present when the field is valid.
   */
  valid = "data-valid",
  /**
   * Present when the field is invalid.
   */
  invalid = "data-invalid",
  /**
   * Present when the field is filled.
   */
  filled = "data-filled",
  /**
   * Present when the field control is focused.
   */
  focused = "data-focused",
}