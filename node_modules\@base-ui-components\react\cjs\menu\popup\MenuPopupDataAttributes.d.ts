export declare enum MenuPopupDataAttributes {
  /**
   * Present when the menu is open.
   */
  open = "data-open",
  /**
   * Present when the menu is closed.
   */
  closed = "data-closed",
  /**
   * Present when the menu is animating in.
   */
  startingStyle = "data-starting-style",
  /**
   * Present when the menu is animating out.
   */
  endingStyle = "data-ending-style",
  /**
   * Indicates which side the popup is positioned relative to the trigger.
   * @type {'top' | 'bottom' | 'left' | 'right' | 'inline-end' | 'inline-start'}
   */
  side = "data-side",
  /**
   * Indicates how the popup is aligned relative to specified side.
   * @type {'start' | 'center' | 'end'}
   */
  align = "data-align",
  /**
   * Present if animations should be instant.
   * @type {'click' | 'dismiss' | 'group'}
   */
  instant = "data-instant",
}