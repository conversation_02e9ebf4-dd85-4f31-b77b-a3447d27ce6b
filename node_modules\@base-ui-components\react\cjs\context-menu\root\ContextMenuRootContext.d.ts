import * as React from 'react';
import type { BaseOpenChangeReason } from "../../utils/translateOpenChangeReason.js";
export interface ContextMenuRootContext {
  anchor: {
    getBoundingClientRect: () => DOMRect;
  };
  setAnchor: React.Dispatch<React.SetStateAction<ContextMenuRootContext['anchor']>>;
  backdropRef: React.RefObject<HTMLDivElement | null>;
  internalBackdropRef: React.RefObject<HTMLDivElement | null>;
  actionsRef: React.RefObject<{
    setOpen: (nextOpen: boolean, event?: Event, reason?: BaseOpenChangeReason) => void;
  } | null>;
  positionerRef: React.RefObject<HTMLElement | null>;
  allowMouseUpTriggerRef: React.RefObject<boolean>;
  rootId: string | undefined;
}
export declare const ContextMenuRootContext: React.Context<ContextMenuRootContext | undefined>;
export declare function useContextMenuRootContext(optional: false): ContextMenuRootContext;
export declare function useContextMenuRootContext(optional?: true): ContextMenuRootContext | undefined;