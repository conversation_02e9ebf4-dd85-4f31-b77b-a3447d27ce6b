"use strict";
'use client';

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.AccordionTrigger = void 0;
var React = _interopRequireWildcard(require("react"));
var _collapsibleOpenStateMapping = require("../../utils/collapsibleOpenStateMapping");
var _useModernLayoutEffect = require("../../utils/useModernLayoutEffect");
var _useRenderElement = require("../../utils/useRenderElement");
var _useButton = require("../../use-button");
var _CollapsibleRootContext = require("../../collapsible/root/CollapsibleRootContext");
var _AccordionItemContext = require("../item/AccordionItemContext");
/**
 * A button that opens and closes the corresponding panel.
 * Renders a `<button>` element.
 *
 * Documentation: [Base UI Accordion](https://base-ui.com/react/components/accordion)
 */

const AccordionTrigger = exports.AccordionTrigger = /*#__PURE__*/React.forwardRef(function AccordionTrigger(componentProps, forwardedRef) {
  const {
    disabled: disabledProp,
    className,
    id: idProp,
    render,
    nativeButton = true,
    ...elementProps
  } = componentProps;
  const {
    panelId,
    open,
    handleTrigger,
    disabled: contextDisabled
  } = (0, _CollapsibleRootContext.useCollapsibleRootContext)();
  const disabled = disabledProp ?? contextDisabled;
  const {
    getButtonProps,
    buttonRef
  } = (0, _useButton.useButton)({
    disabled,
    focusableWhenDisabled: true,
    native: nativeButton
  });
  const {
    state,
    setTriggerId,
    triggerId: id
  } = (0, _AccordionItemContext.useAccordionItemContext)();
  (0, _useModernLayoutEffect.useModernLayoutEffect)(() => {
    if (idProp) {
      setTriggerId(idProp);
    }
    return () => {
      setTriggerId(undefined);
    };
  }, [idProp, setTriggerId]);
  const props = React.useMemo(() => ({
    'aria-controls': open ? panelId : undefined,
    'aria-expanded': open,
    disabled,
    id,
    onClick: handleTrigger
  }), [panelId, disabled, id, open, handleTrigger]);
  const element = (0, _useRenderElement.useRenderElement)('button', componentProps, {
    state,
    ref: [forwardedRef, buttonRef],
    props: [props, elementProps, getButtonProps],
    customStyleHookMapping: _collapsibleOpenStateMapping.triggerOpenStateMapping
  });
  return element;
});
if (process.env.NODE_ENV !== "production") AccordionTrigger.displayName = "AccordionTrigger";