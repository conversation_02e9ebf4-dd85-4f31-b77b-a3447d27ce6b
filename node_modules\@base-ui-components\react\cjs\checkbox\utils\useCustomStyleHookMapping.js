"use strict";
'use client';

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useCustomStyleHookMapping = useCustomStyleHookMapping;
var React = _interopRequireWildcard(require("react"));
var _CheckboxRootDataAttributes = require("../root/CheckboxRootDataAttributes");
function useCustomStyleHookMapping(state) {
  return React.useMemo(() => ({
    checked(value) {
      if (state.indeterminate) {
        // `data-indeterminate` is already handled by the `indeterminate` prop.
        return {};
      }
      if (value) {
        return {
          [_CheckboxRootDataAttributes.CheckboxRootDataAttributes.checked]: ''
        };
      }
      return {
        [_CheckboxRootDataAttributes.CheckboxRootDataAttributes.unchecked]: ''
      };
    }
  }), [state.indeterminate]);
}