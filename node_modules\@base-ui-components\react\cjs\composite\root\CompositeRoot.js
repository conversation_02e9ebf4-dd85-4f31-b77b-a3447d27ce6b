"use strict";
'use client';

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.CompositeRoot = CompositeRoot;
var React = _interopRequireWildcard(require("react"));
var _CompositeList = require("../list/CompositeList");
var _useCompositeRoot = require("./useCompositeRoot");
var _CompositeRootContext = require("./CompositeRootContext");
var _useEventCallback = require("../../utils/useEventCallback");
var _useRenderElement = require("../../utils/useRenderElement");
var _DirectionContext = require("../../direction-provider/DirectionContext");
var _jsxRuntime = require("react/jsx-runtime");
const COMPOSITE_ROOT_STATE = {};

/**
 * @internal
 */
function CompositeRoot(componentProps) {
  const {
    render,
    className,
    highlightedIndex: highlightedIndexProp,
    onHighlightedIndexChange: onHighlightedIndexChangeProp,
    orientation,
    dense,
    itemSizes,
    loop,
    cols,
    enableHomeAndEndKeys,
    onMapChange: onMapChangeProp,
    stopEventPropagation,
    rootRef,
    disabledIndices,
    modifierKeys,
    highlightItemOnHover = false,
    ...elementProps
  } = componentProps;
  const direction = (0, _DirectionContext.useDirection)();
  const {
    props,
    highlightedIndex,
    onHighlightedIndexChange,
    elementsRef,
    onMapChange: onMapChangeUnwrapped
  } = (0, _useCompositeRoot.useCompositeRoot)({
    itemSizes,
    cols,
    loop,
    dense,
    orientation,
    highlightedIndex: highlightedIndexProp,
    onHighlightedIndexChange: onHighlightedIndexChangeProp,
    rootRef,
    stopEventPropagation,
    enableHomeAndEndKeys,
    direction,
    disabledIndices,
    modifierKeys
  });
  const onMapChange = (0, _useEventCallback.useEventCallback)(newMap => {
    onMapChangeProp?.(newMap);
    onMapChangeUnwrapped(newMap);
  });
  const element = (0, _useRenderElement.useRenderElement)('div', componentProps, {
    state: COMPOSITE_ROOT_STATE,
    props: [props, elementProps]
  });
  const contextValue = React.useMemo(() => ({
    highlightedIndex,
    onHighlightedIndexChange,
    highlightItemOnHover
  }), [highlightedIndex, onHighlightedIndexChange, highlightItemOnHover]);
  return /*#__PURE__*/(0, _jsxRuntime.jsx)(_CompositeRootContext.CompositeRootContext.Provider, {
    value: contextValue,
    children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_CompositeList.CompositeList, {
      elementsRef: elementsRef,
      onMapChange: onMapChange,
      children: element
    })
  });
}