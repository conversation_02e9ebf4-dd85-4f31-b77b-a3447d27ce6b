import { add, alter, create, drop, get, set } from "ronin";

export default () => [
  create.model({
    slug: "RONIN_TEMP_user",
    fields: {
      email: { required: true, unique: true, type: "string" },
      emailVerified: { required: true, defaultValue: false, type: "boolean" },
      image: { type: "blob" },
      name: { required: true, type: "string" },
      username: { unique: true, type: "string" },
      displayUsername: { type: "string" },
      slug: { required: true, unique: true, type: "string" },
      role: { required: true, defaultValue: "student", type: "string" },
      isActive: { required: true, defaultValue: true, type: "boolean" },
      createdAt: { required: true, type: "date" },
      updatedAt: { required: true, type: "date" },
      teacherId: { type: "string" },
      grade: { type: "string" },
      classId: { type: "string" },
      isIndependent: { defaultValue: true, type: "boolean" },
      schoolId: { type: "string" },
      department: { type: "string" },
      subjects: { type: "string" },
      isVerified: { defaultValue: false, type: "boolean" },
      schoolName: { type: "string" },
      schoolAddress: { type: "string" },
      schoolPlaceId: { type: "string" },
      schoolType: { type: "string" },
      schoolDistrict: { type: "string" },
      studentCount: { type: "number" },
      teacherCount: { type: "number" },
    },
    idPrefix: "use",
  }),
  add.RONIN_TEMP_users.with(() => get.users()),
  drop.model("user"),
  alter.model("RONIN_TEMP_user").to({ slug: "user", name: "User", pluralName: "Users" }),
  create.model({
    slug: "RONIN_TEMP_user",
    fields: {
      email: { required: false, unique: true, type: "string" },
      emailVerified: { required: false, defaultValue: false, type: "boolean" },
      image: { type: "blob", required: false },
      name: { required: false, type: "string" },
      username: { unique: true, type: "string", required: false },
      displayUsername: { type: "string", required: false },
      slug: { required: false, unique: true, type: "string" },
      role: { required: false, defaultValue: "student", type: "string" },
      isActive: { required: false, defaultValue: true, type: "boolean" },
      createdAt: { required: false, type: "date" },
      updatedAt: { required: false, type: "date" },
      teacherId: { type: "string", required: false },
      grade: { type: "string", required: false },
      classId: { type: "string", required: false },
      isIndependent: { defaultValue: true, type: "boolean", required: false },
      schoolId: { type: "string", required: false },
      department: { type: "string", required: false },
      subjects: { type: "string", required: false },
      isVerified: { defaultValue: false, type: "boolean", required: false },
      schoolName: { type: "string", required: false },
      schoolAddress: { type: "string", required: false },
      schoolPlaceId: { type: "string", required: false },
      schoolType: { type: "string", required: false },
      schoolDistrict: { type: "string", required: false },
      studentCount: { type: "number", required: false },
      teacherCount: { type: "number", required: false },
    },
    idPrefix: "use",
  }),
  add.RONIN_TEMP_users.with(() =>
    get.users({
      selecting: [
        "email",
        "emailVerified",
        "image",
        "name",
        "username",
        "displayUsername",
        "slug",
        "role",
        "isActive",
        "createdAt",
        "updatedAt",
        "teacherId",
        "grade",
        "classId",
        "isIndependent",
        "schoolId",
        "department",
        "subjects",
        "isVerified",
        "schoolName",
        "schoolAddress",
        "schoolPlaceId",
        "schoolType",
        "schoolDistrict",
        "studentCount",
        "teacherCount",
      ],
    })
  ),
  set.RONIN_TEMP_users({ with: { emailVerified: { being: null } }, to: { emailVerified: false } }),
  alter.model("RONIN_TEMP_users").alter.field("emailVerified").to({ required: true }),
  drop.model("user"),
  alter.model("RONIN_TEMP_user").to({ slug: "user", name: "User", pluralName: "Users" }),
  alter.model("user").create.field({ slug: "RONIN_TEMP_image", type: "blob" }),
  set.users.to.RONIN_TEMP_image(f => f.image),
  alter.model("user").drop.field("image"),
  alter.model("user").alter.field("RONIN_TEMP_image").to({ slug: "image" }),
  create.model({
    slug: "RONIN_TEMP_user",
    fields: {
      email: { required: false, unique: true, type: "string" },
      emailVerified: { required: false, defaultValue: false, type: "boolean" },
      image: { type: "blob", required: false },
      name: { required: false, type: "string" },
      username: { unique: true, type: "string", required: false },
      displayUsername: { type: "string", required: false },
      slug: { required: false, unique: true, type: "string" },
      role: { required: false, defaultValue: "student", type: "string" },
      isActive: { required: false, defaultValue: true, type: "boolean" },
      createdAt: { required: false, type: "date" },
      updatedAt: { required: false, type: "date" },
      teacherId: { type: "string", required: false },
      grade: { type: "string", required: false },
      classId: { type: "string", required: false },
      isIndependent: { defaultValue: true, type: "boolean", required: false },
      schoolId: { type: "string", required: false },
      department: { type: "string", required: false },
      subjects: { type: "string", required: false },
      isVerified: { defaultValue: false, type: "boolean", required: false },
      schoolName: { type: "string", required: false },
      schoolAddress: { type: "string", required: false },
      schoolPlaceId: { type: "string", required: false },
      schoolType: { type: "string", required: false },
      schoolDistrict: { type: "string", required: false },
      studentCount: { type: "number", required: false },
      teacherCount: { type: "number", required: false },
    },
    idPrefix: "use",
  }),
  add.RONIN_TEMP_users.with(() =>
    get.users({
      selecting: [
        "email",
        "emailVerified",
        "image",
        "name",
        "username",
        "displayUsername",
        "slug",
        "role",
        "isActive",
        "createdAt",
        "updatedAt",
        "teacherId",
        "grade",
        "classId",
        "isIndependent",
        "schoolId",
        "department",
        "subjects",
        "isVerified",
        "schoolName",
        "schoolAddress",
        "schoolPlaceId",
        "schoolType",
        "schoolDistrict",
        "studentCount",
        "teacherCount",
      ],
    })
  ),
  set.RONIN_TEMP_users({ with: { name: { being: null } }, to: { name: "" } }),
  alter.model("RONIN_TEMP_users").alter.field("name").to({ required: true }),
  drop.model("user"),
  alter.model("RONIN_TEMP_user").to({ slug: "user", name: "User", pluralName: "Users" }),
  create.model({
    slug: "RONIN_TEMP_user",
    fields: {
      email: { required: true, unique: true, type: "string" },
      emailVerified: { required: true, defaultValue: false, type: "boolean" },
      image: { type: "blob" },
      name: { required: true, type: "string" },
      username: { unique: true, type: "string" },
      displayUsername: { type: "string" },
      slug: { required: true, unique: true, type: "string" },
      role: { required: true, defaultValue: "student", type: "string" },
      isActive: { required: true, defaultValue: true, type: "boolean" },
      createdAt: { required: true, type: "date" },
      updatedAt: { required: true, type: "date" },
      teacherId: { type: "string" },
      grade: { type: "string" },
      classId: { type: "string" },
      isIndependent: { defaultValue: true, type: "boolean" },
      schoolId: { type: "string" },
      department: { type: "string" },
      subjects: { type: "string" },
      isVerified: { defaultValue: false, type: "boolean" },
      schoolName: { type: "string" },
      schoolAddress: { type: "string" },
      schoolPlaceId: { type: "string" },
      schoolType: { type: "string" },
      schoolDistrict: { type: "string" },
      studentCount: { type: "number" },
      teacherCount: { type: "number" },
    },
    idPrefix: "use",
  }),
  add.RONIN_TEMP_users.with(() => get.users()),
  drop.model("user"),
  alter.model("RONIN_TEMP_user").to({ slug: "user", name: "User", pluralName: "Users" }),
  alter.model("user").create.field({ slug: "RONIN_TEMP_displayUsername", type: "string" }),
  set.users.to.RONIN_TEMP_displayUsername(f => f.displayUsername),
  alter.model("user").drop.field("displayUsername"),
  alter.model("user").alter.field("RONIN_TEMP_displayUsername").to({ slug: "displayUsername" }),
  create.model({
    slug: "RONIN_TEMP_user",
    fields: {
      email: { required: true, unique: true, type: "string" },
      emailVerified: { required: true, defaultValue: false, type: "boolean" },
      image: { type: "blob" },
      name: { required: true, type: "string" },
      username: { unique: true, type: "string" },
      displayUsername: { type: "string" },
      slug: { required: true, unique: true, type: "string" },
      role: { required: true, defaultValue: "student", type: "string" },
      isActive: { required: true, defaultValue: true, type: "boolean" },
      createdAt: { required: true, type: "date" },
      updatedAt: { required: true, type: "date" },
      teacherId: { type: "string" },
      grade: { type: "string" },
      classId: { type: "string" },
      isIndependent: { defaultValue: true, type: "boolean" },
      schoolId: { type: "string" },
      department: { type: "string" },
      subjects: { type: "string" },
      isVerified: { defaultValue: false, type: "boolean" },
      schoolName: { type: "string" },
      schoolAddress: { type: "string" },
      schoolPlaceId: { type: "string" },
      schoolType: { type: "string" },
      schoolDistrict: { type: "string" },
      studentCount: { type: "number" },
      teacherCount: { type: "number" },
    },
    idPrefix: "use",
  }),
  add.RONIN_TEMP_users.with(() => get.users()),
  drop.model("user"),
  alter.model("RONIN_TEMP_user").to({ slug: "user", name: "User", pluralName: "Users" }),
  alter.model("user").create.field({
    slug: "RONIN_TEMP_role",
    required: true,
    defaultValue: "student",
    type: "string",
  }),
  set.users.to.RONIN_TEMP_role(f => f.role),
  alter.model("user").drop.field("role"),
  alter.model("user").alter.field("RONIN_TEMP_role").to({ slug: "role" }),
  alter.model("user").create.field({
    slug: "RONIN_TEMP_isActive",
    required: true,
    defaultValue: true,
    type: "boolean",
  }),
  set.users.to.RONIN_TEMP_isActive(f => f.isActive),
  alter.model("user").drop.field("isActive"),
  alter.model("user").alter.field("RONIN_TEMP_isActive").to({ slug: "isActive" }),
  create.model({
    slug: "RONIN_TEMP_user",
    fields: {
      email: { required: false, unique: true, type: "string" },
      emailVerified: { required: false, defaultValue: false, type: "boolean" },
      image: { type: "blob", required: false },
      name: { required: false, type: "string" },
      username: { unique: true, type: "string", required: false },
      displayUsername: { type: "string", required: false },
      slug: { required: false, unique: true, type: "string" },
      role: { required: false, defaultValue: "student", type: "string" },
      isActive: { required: false, defaultValue: true, type: "boolean" },
      createdAt: { required: false, type: "date" },
      updatedAt: { required: false, type: "date" },
      teacherId: { type: "string", required: false },
      grade: { type: "string", required: false },
      classId: { type: "string", required: false },
      isIndependent: { defaultValue: true, type: "boolean", required: false },
      schoolId: { type: "string", required: false },
      department: { type: "string", required: false },
      subjects: { type: "string", required: false },
      isVerified: { defaultValue: false, type: "boolean", required: false },
      schoolName: { type: "string", required: false },
      schoolAddress: { type: "string", required: false },
      schoolPlaceId: { type: "string", required: false },
      schoolType: { type: "string", required: false },
      schoolDistrict: { type: "string", required: false },
      studentCount: { type: "number", required: false },
      teacherCount: { type: "number", required: false },
    },
    idPrefix: "use",
  }),
  add.RONIN_TEMP_users.with(() =>
    get.users({
      selecting: [
        "email",
        "emailVerified",
        "image",
        "name",
        "username",
        "displayUsername",
        "slug",
        "role",
        "isActive",
        "createdAt",
        "updatedAt",
        "teacherId",
        "grade",
        "classId",
        "isIndependent",
        "schoolId",
        "department",
        "subjects",
        "isVerified",
        "schoolName",
        "schoolAddress",
        "schoolPlaceId",
        "schoolType",
        "schoolDistrict",
        "studentCount",
        "teacherCount",
      ],
    })
  ),
  set.RONIN_TEMP_users({ with: { createdAt: { being: null } }, to: { createdAt: "" } }),
  alter.model("RONIN_TEMP_users").alter.field("createdAt").to({ required: true }),
  drop.model("user"),
  alter.model("RONIN_TEMP_user").to({ slug: "user", name: "User", pluralName: "Users" }),
  create.model({
    slug: "RONIN_TEMP_user",
    fields: {
      email: { required: false, unique: true, type: "string" },
      emailVerified: { required: false, defaultValue: false, type: "boolean" },
      image: { type: "blob", required: false },
      name: { required: false, type: "string" },
      username: { unique: true, type: "string", required: false },
      displayUsername: { type: "string", required: false },
      slug: { required: false, unique: true, type: "string" },
      role: { required: false, defaultValue: "student", type: "string" },
      isActive: { required: false, defaultValue: true, type: "boolean" },
      createdAt: { required: false, type: "date" },
      updatedAt: { required: false, type: "date" },
      teacherId: { type: "string", required: false },
      grade: { type: "string", required: false },
      classId: { type: "string", required: false },
      isIndependent: { defaultValue: true, type: "boolean", required: false },
      schoolId: { type: "string", required: false },
      department: { type: "string", required: false },
      subjects: { type: "string", required: false },
      isVerified: { defaultValue: false, type: "boolean", required: false },
      schoolName: { type: "string", required: false },
      schoolAddress: { type: "string", required: false },
      schoolPlaceId: { type: "string", required: false },
      schoolType: { type: "string", required: false },
      schoolDistrict: { type: "string", required: false },
      studentCount: { type: "number", required: false },
      teacherCount: { type: "number", required: false },
    },
    idPrefix: "use",
  }),
  add.RONIN_TEMP_users.with(() =>
    get.users({
      selecting: [
        "email",
        "emailVerified",
        "image",
        "name",
        "username",
        "displayUsername",
        "slug",
        "role",
        "isActive",
        "createdAt",
        "updatedAt",
        "teacherId",
        "grade",
        "classId",
        "isIndependent",
        "schoolId",
        "department",
        "subjects",
        "isVerified",
        "schoolName",
        "schoolAddress",
        "schoolPlaceId",
        "schoolType",
        "schoolDistrict",
        "studentCount",
        "teacherCount",
      ],
    })
  ),
  set.RONIN_TEMP_users({ with: { updatedAt: { being: null } }, to: { updatedAt: "" } }),
  alter.model("RONIN_TEMP_users").alter.field("updatedAt").to({ required: true }),
  drop.model("user"),
  alter.model("RONIN_TEMP_user").to({ slug: "user", name: "User", pluralName: "Users" }),
  alter.model("user").create.field({ slug: "RONIN_TEMP_teacherId", type: "string" }),
  set.users.to.RONIN_TEMP_teacherId(f => f.teacherId),
  alter.model("user").drop.field("teacherId"),
  alter.model("user").alter.field("RONIN_TEMP_teacherId").to({ slug: "teacherId" }),
  alter.model("user").create.field({ slug: "RONIN_TEMP_grade", type: "string" }),
  set.users.to.RONIN_TEMP_grade(f => f.grade),
  alter.model("user").drop.field("grade"),
  alter.model("user").alter.field("RONIN_TEMP_grade").to({ slug: "grade" }),
  alter.model("user").create.field({ slug: "RONIN_TEMP_classId", type: "string" }),
  set.users.to.RONIN_TEMP_classId(f => f.classId),
  alter.model("user").drop.field("classId"),
  alter.model("user").alter.field("RONIN_TEMP_classId").to({ slug: "classId" }),
  alter.model("user").create.field({ slug: "RONIN_TEMP_isIndependent", defaultValue: true, type: "boolean" }),
  set.users.to.RONIN_TEMP_isIndependent(f => f.isIndependent),
  alter.model("user").drop.field("isIndependent"),
  alter.model("user").alter.field("RONIN_TEMP_isIndependent").to({ slug: "isIndependent" }),
  alter.model("user").create.field({ slug: "RONIN_TEMP_schoolId", type: "string" }),
  set.users.to.RONIN_TEMP_schoolId(f => f.schoolId),
  alter.model("user").drop.field("schoolId"),
  alter.model("user").alter.field("RONIN_TEMP_schoolId").to({ slug: "schoolId" }),
  alter.model("user").create.field({ slug: "RONIN_TEMP_department", type: "string" }),
  set.users.to.RONIN_TEMP_department(f => f.department),
  alter.model("user").drop.field("department"),
  alter.model("user").alter.field("RONIN_TEMP_department").to({ slug: "department" }),
  alter.model("user").create.field({ slug: "RONIN_TEMP_subjects", type: "string" }),
  set.users.to.RONIN_TEMP_subjects(f => f.subjects),
  alter.model("user").drop.field("subjects"),
  alter.model("user").alter.field("RONIN_TEMP_subjects").to({ slug: "subjects" }),
  alter.model("user").create.field({ slug: "RONIN_TEMP_isVerified", defaultValue: false, type: "boolean" }),
  set.users.to.RONIN_TEMP_isVerified(f => f.isVerified),
  alter.model("user").drop.field("isVerified"),
  alter.model("user").alter.field("RONIN_TEMP_isVerified").to({ slug: "isVerified" }),
  alter.model("user").create.field({ slug: "RONIN_TEMP_schoolName", type: "string" }),
  set.users.to.RONIN_TEMP_schoolName(f => f.schoolName),
  alter.model("user").drop.field("schoolName"),
  alter.model("user").alter.field("RONIN_TEMP_schoolName").to({ slug: "schoolName" }),
  alter.model("user").create.field({ slug: "RONIN_TEMP_schoolAddress", type: "string" }),
  set.users.to.RONIN_TEMP_schoolAddress(f => f.schoolAddress),
  alter.model("user").drop.field("schoolAddress"),
  alter.model("user").alter.field("RONIN_TEMP_schoolAddress").to({ slug: "schoolAddress" }),
  alter.model("user").create.field({ slug: "RONIN_TEMP_schoolPlaceId", type: "string" }),
  set.users.to.RONIN_TEMP_schoolPlaceId(f => f.schoolPlaceId),
  alter.model("user").drop.field("schoolPlaceId"),
  alter.model("user").alter.field("RONIN_TEMP_schoolPlaceId").to({ slug: "schoolPlaceId" }),
  alter.model("user").create.field({ slug: "RONIN_TEMP_schoolType", type: "string" }),
  set.users.to.RONIN_TEMP_schoolType(f => f.schoolType),
  alter.model("user").drop.field("schoolType"),
  alter.model("user").alter.field("RONIN_TEMP_schoolType").to({ slug: "schoolType" }),
  alter.model("user").create.field({ slug: "RONIN_TEMP_schoolDistrict", type: "string" }),
  set.users.to.RONIN_TEMP_schoolDistrict(f => f.schoolDistrict),
  alter.model("user").drop.field("schoolDistrict"),
  alter.model("user").alter.field("RONIN_TEMP_schoolDistrict").to({ slug: "schoolDistrict" }),
  alter.model("user").create.field({ slug: "RONIN_TEMP_studentCount", type: "number" }),
  set.users.to.RONIN_TEMP_studentCount(f => f.studentCount),
  alter.model("user").drop.field("studentCount"),
  alter.model("user").alter.field("RONIN_TEMP_studentCount").to({ slug: "studentCount" }),
  alter.model("user").create.field({ slug: "RONIN_TEMP_teacherCount", type: "number" }),
  set.users.to.RONIN_TEMP_teacherCount(f => f.teacherCount),
  alter.model("user").drop.field("teacherCount"),
  alter.model("user").alter.field("RONIN_TEMP_teacherCount").to({ slug: "teacherCount" }),
];
