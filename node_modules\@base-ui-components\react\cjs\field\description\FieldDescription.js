"use strict";
'use client';

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.FieldDescription = void 0;
var React = _interopRequireWildcard(require("react"));
var _FieldRootContext = require("../root/FieldRootContext");
var _constants = require("../utils/constants");
var _useBaseUiId = require("../../utils/useBaseUiId");
var _useModernLayoutEffect = require("../../utils/useModernLayoutEffect");
var _useRenderElement = require("../../utils/useRenderElement");
/**
 * A paragraph with additional information about the field.
 * Renders a `<p>` element.
 *
 * Documentation: [Base UI Field](https://base-ui.com/react/components/field)
 */
const FieldDescription = exports.FieldDescription = /*#__PURE__*/React.forwardRef(function FieldDescription(componentProps, forwardedRef) {
  const {
    render,
    id: idProp,
    className,
    ...elementProps
  } = componentProps;
  const {
    state
  } = (0, _FieldRootContext.useFieldRootContext)(false);
  const id = (0, _useBaseUiId.useBaseUiId)(idProp);
  const {
    setMessageIds
  } = (0, _FieldRootContext.useFieldRootContext)();
  (0, _useModernLayoutEffect.useModernLayoutEffect)(() => {
    if (!id) {
      return undefined;
    }
    setMessageIds(v => v.concat(id));
    return () => {
      setMessageIds(v => v.filter(item => item !== id));
    };
  }, [id, setMessageIds]);
  const element = (0, _useRenderElement.useRenderElement)('p', componentProps, {
    ref: forwardedRef,
    state,
    props: [{
      id
    }, elementProps],
    customStyleHookMapping: _constants.fieldValidityMapping
  });
  return element;
});
if (process.env.NODE_ENV !== "production") FieldDescription.displayName = "FieldDescription";