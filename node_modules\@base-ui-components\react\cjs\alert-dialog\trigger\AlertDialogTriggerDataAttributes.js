"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.AlertDialogTriggerDataAttributes = void 0;
var _popupStateMapping = require("../../utils/popupStateMapping");
let AlertDialogTriggerDataAttributes = exports.AlertDialogTriggerDataAttributes = function (AlertDialogTriggerDataAttributes) {
  /**
   * Present when the trigger is disabled.
   */
  AlertDialogTriggerDataAttributes["disabled"] = "data-disabled";
  /**
   * Present when the corresponding dialog is open.
   */
  AlertDialogTriggerDataAttributes[AlertDialogTriggerDataAttributes["popupOpen"] = _popupStateMapping.CommonTriggerDataAttributes.popupOpen] = "popupOpen";
  return AlertDialogTriggerDataAttributes;
}({});