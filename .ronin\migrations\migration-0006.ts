import { add, alter, create, drop, get, set } from "ronin";

export default () => [
  create.model({
    slug: "educationalContext",
    pluralSlug: "educationalContexts",
    fields: {
      name: { required: true, type: "string" },
      type: { required: true, type: "string" },
      description: { type: "string" },
      defaultGradeLevels: { type: "string" },
      teacherId: { required: true, target: "teacher", type: "link" },
      isActive: { required: true, defaultValue: true, type: "boolean" },
      createdAt: { required: true, type: "date" },
      updatedAt: { required: true, type: "date" },
    },
  }),
  create.model({
    slug: "gradeLevel",
    pluralSlug: "gradeLevels",
    fields: {
      name: { required: true, type: "string" },
      code: { type: "string" },
      description: { type: "string" },
      category: { required: true, type: "string" },
      educationType: { required: true, type: "string" },
      teacherId: { required: true, target: "teacher", type: "link" },
      schoolId: { target: "school", type: "link" },
      sortOrder: { defaultValue: 0, type: "number" },
      isActive: { required: true, defaultValue: true, type: "boolean" },
      createdAt: { required: true, type: "date" },
      updatedAt: { required: true, type: "date" },
    },
  }),
  create.model({
    slug: "RONIN_TEMP_user",
    fields: {
      email: { required: true, unique: true, type: "string" },
      emailVerified: { required: true, defaultValue: false, type: "boolean" },
      image: { type: "blob" },
      name: { required: true, type: "string" },
      username: { unique: true, type: "string" },
      displayUsername: { type: "string" },
      slug: { required: true, unique: true, type: "string" },
      role: { required: true, defaultValue: "student", type: "string" },
      isActive: { required: true, defaultValue: true, type: "boolean" },
      createdAt: { required: true, type: "date" },
      updatedAt: { required: true, type: "date" },
      teacherId: { type: "string" },
      grade: { type: "string" },
      classId: { type: "string" },
      isIndependent: { defaultValue: true, type: "boolean" },
      schoolId: { type: "string" },
      department: { type: "string" },
      subjects: { type: "string" },
      isVerified: { defaultValue: false, type: "boolean" },
      schoolName: { type: "string" },
      schoolAddress: { type: "string" },
      schoolPlaceId: { type: "string" },
      schoolType: { type: "string" },
      schoolDistrict: { type: "string" },
      studentCount: { type: "number" },
      teacherCount: { type: "number" },
    },
    idPrefix: "use",
  }),
  add.RONIN_TEMP_users.with(() => get.users()),
  drop.model("user"),
  alter.model("RONIN_TEMP_user").to({ slug: "user", name: "User", pluralName: "Users" }),
  create.model({
    slug: "RONIN_TEMP_user",
    fields: {
      email: { required: false, unique: true, type: "string" },
      emailVerified: { required: false, defaultValue: false, type: "boolean" },
      image: { type: "blob", required: false },
      name: { required: false, type: "string" },
      username: { unique: true, type: "string", required: false },
      displayUsername: { type: "string", required: false },
      slug: { required: false, unique: true, type: "string" },
      role: { required: false, defaultValue: "student", type: "string" },
      isActive: { required: false, defaultValue: true, type: "boolean" },
      createdAt: { required: false, type: "date" },
      updatedAt: { required: false, type: "date" },
      teacherId: { type: "string", required: false },
      grade: { type: "string", required: false },
      classId: { type: "string", required: false },
      isIndependent: { defaultValue: true, type: "boolean", required: false },
      schoolId: { type: "string", required: false },
      department: { type: "string", required: false },
      subjects: { type: "string", required: false },
      isVerified: { defaultValue: false, type: "boolean", required: false },
      schoolName: { type: "string", required: false },
      schoolAddress: { type: "string", required: false },
      schoolPlaceId: { type: "string", required: false },
      schoolType: { type: "string", required: false },
      schoolDistrict: { type: "string", required: false },
      studentCount: { type: "number", required: false },
      teacherCount: { type: "number", required: false },
    },
    idPrefix: "use",
  }),
  add.RONIN_TEMP_users.with(() =>
    get.users({
      selecting: [
        "email",
        "emailVerified",
        "image",
        "name",
        "username",
        "displayUsername",
        "slug",
        "role",
        "isActive",
        "createdAt",
        "updatedAt",
        "teacherId",
        "grade",
        "classId",
        "isIndependent",
        "schoolId",
        "department",
        "subjects",
        "isVerified",
        "schoolName",
        "schoolAddress",
        "schoolPlaceId",
        "schoolType",
        "schoolDistrict",
        "studentCount",
        "teacherCount",
      ],
    })
  ),
  set.RONIN_TEMP_users({ with: { emailVerified: { being: null } }, to: { emailVerified: true } }),
  alter.model("RONIN_TEMP_users").alter.field("emailVerified").to({ required: true }),
  drop.model("user"),
  alter.model("RONIN_TEMP_user").to({ slug: "user", name: "User", pluralName: "Users" }),
  alter.model("user").create.field({ slug: "RONIN_TEMP_image", type: "blob" }),
  set.users.to.RONIN_TEMP_image(f => f.image),
  alter.model("user").drop.field("image"),
  alter.model("user").alter.field("RONIN_TEMP_image").to({ slug: "image" }),
  create.model({
    slug: "RONIN_TEMP_user",
    fields: {
      email: { required: false, unique: true, type: "string" },
      emailVerified: { required: false, defaultValue: false, type: "boolean" },
      image: { type: "blob", required: false },
      name: { required: false, type: "string" },
      username: { unique: true, type: "string", required: false },
      displayUsername: { type: "string", required: false },
      slug: { required: false, unique: true, type: "string" },
      role: { required: false, defaultValue: "student", type: "string" },
      isActive: { required: false, defaultValue: true, type: "boolean" },
      createdAt: { required: false, type: "date" },
      updatedAt: { required: false, type: "date" },
      teacherId: { type: "string", required: false },
      grade: { type: "string", required: false },
      classId: { type: "string", required: false },
      isIndependent: { defaultValue: true, type: "boolean", required: false },
      schoolId: { type: "string", required: false },
      department: { type: "string", required: false },
      subjects: { type: "string", required: false },
      isVerified: { defaultValue: false, type: "boolean", required: false },
      schoolName: { type: "string", required: false },
      schoolAddress: { type: "string", required: false },
      schoolPlaceId: { type: "string", required: false },
      schoolType: { type: "string", required: false },
      schoolDistrict: { type: "string", required: false },
      studentCount: { type: "number", required: false },
      teacherCount: { type: "number", required: false },
    },
    idPrefix: "use",
  }),
  add.RONIN_TEMP_users.with(() =>
    get.users({
      selecting: [
        "email",
        "emailVerified",
        "image",
        "name",
        "username",
        "displayUsername",
        "slug",
        "role",
        "isActive",
        "createdAt",
        "updatedAt",
        "teacherId",
        "grade",
        "classId",
        "isIndependent",
        "schoolId",
        "department",
        "subjects",
        "isVerified",
        "schoolName",
        "schoolAddress",
        "schoolPlaceId",
        "schoolType",
        "schoolDistrict",
        "studentCount",
        "teacherCount",
      ],
    })
  ),
  set.RONIN_TEMP_users({ with: { name: { being: null } }, to: { name: "" } }),
  alter.model("RONIN_TEMP_users").alter.field("name").to({ required: true }),
  drop.model("user"),
  alter.model("RONIN_TEMP_user").to({ slug: "user", name: "User", pluralName: "Users" }),
  create.model({
    slug: "RONIN_TEMP_user",
    fields: {
      email: { required: true, unique: true, type: "string" },
      emailVerified: { required: true, defaultValue: false, type: "boolean" },
      image: { type: "blob" },
      name: { required: true, type: "string" },
      username: { unique: true, type: "string" },
      displayUsername: { type: "string" },
      slug: { required: true, unique: true, type: "string" },
      role: { required: true, defaultValue: "student", type: "string" },
      isActive: { required: true, defaultValue: true, type: "boolean" },
      createdAt: { required: true, type: "date" },
      updatedAt: { required: true, type: "date" },
      teacherId: { type: "string" },
      grade: { type: "string" },
      classId: { type: "string" },
      isIndependent: { defaultValue: true, type: "boolean" },
      schoolId: { type: "string" },
      department: { type: "string" },
      subjects: { type: "string" },
      isVerified: { defaultValue: false, type: "boolean" },
      schoolName: { type: "string" },
      schoolAddress: { type: "string" },
      schoolPlaceId: { type: "string" },
      schoolType: { type: "string" },
      schoolDistrict: { type: "string" },
      studentCount: { type: "number" },
      teacherCount: { type: "number" },
    },
    idPrefix: "use",
  }),
  add.RONIN_TEMP_users.with(() => get.users()),
  drop.model("user"),
  alter.model("RONIN_TEMP_user").to({ slug: "user", name: "User", pluralName: "Users" }),
  alter.model("user").create.field({ slug: "RONIN_TEMP_displayUsername", type: "string" }),
  set.users.to.RONIN_TEMP_displayUsername(f => f.displayUsername),
  alter.model("user").drop.field("displayUsername"),
  alter.model("user").alter.field("RONIN_TEMP_displayUsername").to({ slug: "displayUsername" }),
  create.model({
    slug: "RONIN_TEMP_user",
    fields: {
      email: { required: true, unique: true, type: "string" },
      emailVerified: { required: true, defaultValue: false, type: "boolean" },
      image: { type: "blob" },
      name: { required: true, type: "string" },
      username: { unique: true, type: "string" },
      displayUsername: { type: "string" },
      slug: { required: true, unique: true, type: "string" },
      role: { required: true, defaultValue: "student", type: "string" },
      isActive: { required: true, defaultValue: true, type: "boolean" },
      createdAt: { required: true, type: "date" },
      updatedAt: { required: true, type: "date" },
      teacherId: { type: "string" },
      grade: { type: "string" },
      classId: { type: "string" },
      isIndependent: { defaultValue: true, type: "boolean" },
      schoolId: { type: "string" },
      department: { type: "string" },
      subjects: { type: "string" },
      isVerified: { defaultValue: false, type: "boolean" },
      schoolName: { type: "string" },
      schoolAddress: { type: "string" },
      schoolPlaceId: { type: "string" },
      schoolType: { type: "string" },
      schoolDistrict: { type: "string" },
      studentCount: { type: "number" },
      teacherCount: { type: "number" },
    },
    idPrefix: "use",
  }),
  add.RONIN_TEMP_users.with(() => get.users()),
  drop.model("user"),
  alter.model("RONIN_TEMP_user").to({ slug: "user", name: "User", pluralName: "Users" }),
  alter.model("user").create.field({
    slug: "RONIN_TEMP_role",
    required: true,
    defaultValue: "student",
    type: "string",
  }),
  set.users.to.RONIN_TEMP_role(f => f.role),
  alter.model("user").drop.field("role"),
  alter.model("user").alter.field("RONIN_TEMP_role").to({ slug: "role" }),
  alter.model("user").create.field({
    slug: "RONIN_TEMP_isActive",
    required: true,
    defaultValue: true,
    type: "boolean",
  }),
  set.users.to.RONIN_TEMP_isActive(f => f.isActive),
  alter.model("user").drop.field("isActive"),
  alter.model("user").alter.field("RONIN_TEMP_isActive").to({ slug: "isActive" }),
  create.model({
    slug: "RONIN_TEMP_user",
    fields: {
      email: { required: false, unique: true, type: "string" },
      emailVerified: { required: false, defaultValue: false, type: "boolean" },
      image: { type: "blob", required: false },
      name: { required: false, type: "string" },
      username: { unique: true, type: "string", required: false },
      displayUsername: { type: "string", required: false },
      slug: { required: false, unique: true, type: "string" },
      role: { required: false, defaultValue: "student", type: "string" },
      isActive: { required: false, defaultValue: true, type: "boolean" },
      createdAt: { required: false, type: "date" },
      updatedAt: { required: false, type: "date" },
      teacherId: { type: "string", required: false },
      grade: { type: "string", required: false },
      classId: { type: "string", required: false },
      isIndependent: { defaultValue: true, type: "boolean", required: false },
      schoolId: { type: "string", required: false },
      department: { type: "string", required: false },
      subjects: { type: "string", required: false },
      isVerified: { defaultValue: false, type: "boolean", required: false },
      schoolName: { type: "string", required: false },
      schoolAddress: { type: "string", required: false },
      schoolPlaceId: { type: "string", required: false },
      schoolType: { type: "string", required: false },
      schoolDistrict: { type: "string", required: false },
      studentCount: { type: "number", required: false },
      teacherCount: { type: "number", required: false },
    },
    idPrefix: "use",
  }),
  add.RONIN_TEMP_users.with(() =>
    get.users({
      selecting: [
        "email",
        "emailVerified",
        "image",
        "name",
        "username",
        "displayUsername",
        "slug",
        "role",
        "isActive",
        "createdAt",
        "updatedAt",
        "teacherId",
        "grade",
        "classId",
        "isIndependent",
        "schoolId",
        "department",
        "subjects",
        "isVerified",
        "schoolName",
        "schoolAddress",
        "schoolPlaceId",
        "schoolType",
        "schoolDistrict",
        "studentCount",
        "teacherCount",
      ],
    })
  ),
  set.RONIN_TEMP_users({ with: { createdAt: { being: null } }, to: { createdAt: "" } }),
  alter.model("RONIN_TEMP_users").alter.field("createdAt").to({ required: true }),
  drop.model("user"),
  alter.model("RONIN_TEMP_user").to({ slug: "user", name: "User", pluralName: "Users" }),
  create.model({
    slug: "RONIN_TEMP_user",
    fields: {
      email: { required: false, unique: true, type: "string" },
      emailVerified: { required: false, defaultValue: false, type: "boolean" },
      image: { type: "blob", required: false },
      name: { required: false, type: "string" },
      username: { unique: true, type: "string", required: false },
      displayUsername: { type: "string", required: false },
      slug: { required: false, unique: true, type: "string" },
      role: { required: false, defaultValue: "student", type: "string" },
      isActive: { required: false, defaultValue: true, type: "boolean" },
      createdAt: { required: false, type: "date" },
      updatedAt: { required: false, type: "date" },
      teacherId: { type: "string", required: false },
      grade: { type: "string", required: false },
      classId: { type: "string", required: false },
      isIndependent: { defaultValue: true, type: "boolean", required: false },
      schoolId: { type: "string", required: false },
      department: { type: "string", required: false },
      subjects: { type: "string", required: false },
      isVerified: { defaultValue: false, type: "boolean", required: false },
      schoolName: { type: "string", required: false },
      schoolAddress: { type: "string", required: false },
      schoolPlaceId: { type: "string", required: false },
      schoolType: { type: "string", required: false },
      schoolDistrict: { type: "string", required: false },
      studentCount: { type: "number", required: false },
      teacherCount: { type: "number", required: false },
    },
    idPrefix: "use",
  }),
  add.RONIN_TEMP_users.with(() =>
    get.users({
      selecting: [
        "email",
        "emailVerified",
        "image",
        "name",
        "username",
        "displayUsername",
        "slug",
        "role",
        "isActive",
        "createdAt",
        "updatedAt",
        "teacherId",
        "grade",
        "classId",
        "isIndependent",
        "schoolId",
        "department",
        "subjects",
        "isVerified",
        "schoolName",
        "schoolAddress",
        "schoolPlaceId",
        "schoolType",
        "schoolDistrict",
        "studentCount",
        "teacherCount",
      ],
    })
  ),
  set.RONIN_TEMP_users({ with: { updatedAt: { being: null } }, to: { updatedAt: "" } }),
  alter.model("RONIN_TEMP_users").alter.field("updatedAt").to({ required: true }),
  drop.model("user"),
  alter.model("RONIN_TEMP_user").to({ slug: "user", name: "User", pluralName: "Users" }),
];
