export declare enum InputDataAttributes {
  /**
   * Present when the input is disabled.
   */
  disabled = "data-disabled",
  /**
   * Present when the input is in valid state.
   */
  valid = "data-valid",
  /**
   * Present when the input is in invalid state.
   */
  invalid = "data-invalid",
  /**
   * Present when the input has been touched.
   */
  touched = "data-touched",
  /**
   * Present when the input's value has changed.
   */
  dirty = "data-dirty",
  /**
   * Present when the input is filled.
   */
  filled = "data-filled",
  /**
   * Present when the input is focused.
   */
  focused = "data-focused",
}