export declare enum CollapsiblePanelDataAttributes {
  /**
   * Present when the collapsible panel is open.
   */
  open = "data-open",
  /**
   * Present when the collapsible panel is closed.
   */
  closed = "data-closed",
  /**
   * Present when the panel is animating in.
   */
  startingStyle = "data-starting-style",
  /**
   * Present when the panel is animating out.
   */
  endingStyle = "data-ending-style",
}