"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "Backdrop", {
  enumerable: true,
  get: function () {
    return _DialogBackdrop.DialogBackdrop;
  }
});
Object.defineProperty(exports, "Close", {
  enumerable: true,
  get: function () {
    return _DialogClose.DialogClose;
  }
});
Object.defineProperty(exports, "Description", {
  enumerable: true,
  get: function () {
    return _DialogDescription.DialogDescription;
  }
});
Object.defineProperty(exports, "Popup", {
  enumerable: true,
  get: function () {
    return _DialogPopup.DialogPopup;
  }
});
Object.defineProperty(exports, "Portal", {
  enumerable: true,
  get: function () {
    return _DialogPortal.DialogPortal;
  }
});
Object.defineProperty(exports, "Root", {
  enumerable: true,
  get: function () {
    return _DialogRoot.DialogRoot;
  }
});
Object.defineProperty(exports, "Title", {
  enumerable: true,
  get: function () {
    return _DialogTitle.DialogTitle;
  }
});
Object.defineProperty(exports, "Trigger", {
  enumerable: true,
  get: function () {
    return _DialogTrigger.DialogTrigger;
  }
});
var _DialogBackdrop = require("./backdrop/DialogBackdrop");
var _DialogClose = require("./close/DialogClose");
var _DialogDescription = require("./description/DialogDescription");
var _DialogPopup = require("./popup/DialogPopup");
var _DialogPortal = require("./portal/DialogPortal");
var _DialogRoot = require("./root/DialogRoot");
var _DialogTitle = require("./title/DialogTitle");
var _DialogTrigger = require("./trigger/DialogTrigger");