export declare function activeElement(doc: Document): Element | null;
export declare function contains(parent?: Element | null, child?: Element | null): boolean;
export declare function getTarget(event: Event): EventTarget | null;
export declare function isEventTargetWithin(event: Event, node: Node | null | undefined): boolean;
export declare function isRootElement(element: Element): boolean;
export declare function getDocument(node: Element | null): Document;
export declare function isTypeableElement(element: unknown): boolean;
export declare function isTypeableCombobox(element: Element | null): boolean;
export declare function matchesFocusVisible(element: Element | null): boolean;
export declare function getFloatingFocusElement(floatingElement: HTMLElement | null | undefined): HTMLElement | null;