"use strict";
'use client';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useDialogClose = useDialogClose;
var _useButton = require("../../use-button/useButton");
var _mergeProps = require("../../merge-props");
var _useEventCallback = require("../../utils/useEventCallback");
function useDialogClose(params) {
  const {
    open,
    setOpen,
    disabled,
    nativeButton
  } = params;
  const handleClick = (0, _useEventCallback.useEventCallback)(event => {
    if (open) {
      setOpen(false, event.nativeEvent, 'close-press');
    }
  });
  const {
    getButtonProps,
    buttonRef
  } = (0, _useButton.useButton)({
    disabled,
    native: nativeButton
  });
  const getRootProps = externalProps => (0, _mergeProps.mergeProps)({
    onClick: handleClick
  }, externalProps, getButtonProps);
  return {
    getRootProps,
    ref: buttonRef
  };
}