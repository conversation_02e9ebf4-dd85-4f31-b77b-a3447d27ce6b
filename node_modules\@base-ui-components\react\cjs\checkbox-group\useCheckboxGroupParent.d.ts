import * as React from 'react';
export declare function useCheckboxGroupParent(params: useCheckboxGroupParent.Parameters): useCheckboxGroupParent.ReturnValue;
export declare namespace useCheckboxGroupParent {
  interface Parameters {
    allValues?: string[];
    value?: string[];
    onValueChange?: (value: string[], event: Event) => void;
  }
  interface ReturnValue {
    id: string | undefined;
    indeterminate: boolean;
    disabledStatesRef: React.MutableRefObject<Map<string, boolean>>;
    getParentProps: () => {
      id: string | undefined;
      indeterminate: boolean;
      checked: boolean;
      'aria-controls': string;
      onCheckedChange: (checked: boolean, event: Event) => void;
    };
    getChildProps: (name: string) => {
      name: string;
      id: string;
      checked: boolean;
      onCheckedChange: (checked: boolean, event: Event) => void;
    };
  }
}