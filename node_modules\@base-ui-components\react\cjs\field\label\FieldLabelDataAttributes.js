"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.FieldLabelDataAttributes = void 0;
let FieldLabelDataAttributes = exports.FieldLabelDataAttributes = /*#__PURE__*/function (FieldLabelDataAttributes) {
  /**
   * Present when the field is disabled.
   */
  FieldLabelDataAttributes["disabled"] = "data-disabled";
  /**
   * Present when the field is in valid state.
   */
  FieldLabelDataAttributes["valid"] = "data-valid";
  /**
   * Present when the field is in invalid state.
   */
  FieldLabelDataAttributes["invalid"] = "data-invalid";
  /**
   * Present when the field has been touched.
   */
  FieldLabelDataAttributes["touched"] = "data-touched";
  /**
   * Present when the field's value has changed.
   */
  FieldLabelDataAttributes["dirty"] = "data-dirty";
  /**
   * Present when the field is filled.
   */
  FieldLabelDataAttributes["filled"] = "data-filled";
  /**
   * Present when the field control is focused.
   */
  FieldLabelDataAttributes["focused"] = "data-focused";
  return FieldLabelDataAttributes;
}({});