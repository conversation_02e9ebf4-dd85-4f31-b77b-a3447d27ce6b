"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MenuPositionerCssVars = void 0;
let MenuPositionerCssVars = exports.MenuPositionerCssVars = /*#__PURE__*/function (MenuPositionerCssVars) {
  /**
   * The available width between the trigger and the edge of the viewport.
   * @type {number}
   */
  MenuPositionerCssVars["availableWidth"] = "--available-width";
  /**
   * The available height between the trigger and the edge of the viewport.
   * @type {number}
   */
  MenuPositionerCssVars["availableHeight"] = "--available-height";
  /**
   * The anchor's width.
   * @type {number}
   */
  MenuPositionerCssVars["anchorWidth"] = "--anchor-width";
  /**
   * The anchor's height.
   * @type {number}
   */
  MenuPositionerCssVars["anchorHeight"] = "--anchor-height";
  /**
   * The coordinates that this element is anchored to. Used for animations and transitions.
   * @type {string}
   */
  MenuPositionerCssVars["transformOrigin"] = "--transform-origin";
  return MenuPositionerCssVars;
}({});