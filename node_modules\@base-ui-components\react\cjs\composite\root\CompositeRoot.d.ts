import * as React from 'react';
import { type CompositeMetadata } from "../list/CompositeList.js";
import type { BaseUIComponentProps } from "../../utils/types.js";
import type { Dimensions, ModifierKey } from "../composite.js";
/**
 * @internal
 */
export declare function CompositeRoot<Metadata extends {}>(componentProps: CompositeRoot.Props<Metadata>): React.JSX.Element;
export declare namespace CompositeRoot {
  interface State {}
  interface Props<Metadata> extends BaseUIComponentProps<'div', State> {
    orientation?: 'horizontal' | 'vertical' | 'both';
    cols?: number;
    loop?: boolean;
    highlightedIndex?: number;
    onHighlightedIndexChange?: (index: number) => void;
    itemSizes?: Dimensions[];
    dense?: boolean;
    enableHomeAndEndKeys?: boolean;
    onMapChange?: (newMap: Map<Node, CompositeMetadata<Metadata> | null>) => void;
    stopEventPropagation?: boolean;
    rootRef?: React.RefObject<HTMLElement | null>;
    disabledIndices?: number[];
    modifierKeys?: ModifierKey[];
    highlightItemOnHover?: boolean;
  }
}