"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.DialogTriggerDataAttributes = void 0;
var _popupStateMapping = require("../../utils/popupStateMapping");
let DialogTriggerDataAttributes = exports.DialogTriggerDataAttributes = function (DialogTriggerDataAttributes) {
  /**
   * Present when the trigger is disabled.
   */
  DialogTriggerDataAttributes["disabled"] = "data-disabled";
  /**
   * Present when the corresponding dialog is open.
   */
  DialogTriggerDataAttributes[DialogTriggerDataAttributes["popupOpen"] = _popupStateMapping.CommonTriggerDataAttributes.popupOpen] = "popupOpen";
  return DialogTriggerDataAttributes;
}({});