import * as React from 'react';
import type { FieldValidityData } from "../field/root/FieldRoot.js";
export type Errors = Record<string, string | string[]>;
export interface FormContext {
  errors: Errors;
  clearErrors: (name: string | undefined) => void;
  formRef: React.RefObject<{
    fields: Map<string, {
      name: string | undefined;
      validate: () => void;
      validityData: FieldValidityData;
      controlRef: React.RefObject<HTMLElement>;
      getValueRef: React.RefObject<(() => unknown) | undefined>;
    }>;
  }>;
}
export declare const FormContext: React.Context<FormContext>;
export declare function useFormContext(): FormContext;