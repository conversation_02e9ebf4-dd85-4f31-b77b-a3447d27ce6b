"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.CheckboxRootDataAttributes = void 0;
let CheckboxRootDataAttributes = exports.CheckboxRootDataAttributes = /*#__PURE__*/function (CheckboxRootDataAttributes) {
  /**
   * Present when the checkbox is checked.
   */
  CheckboxRootDataAttributes["checked"] = "data-checked";
  /**
   * Present when the checkbox is not checked.
   */
  CheckboxRootDataAttributes["unchecked"] = "data-unchecked";
  /**
   * Present when the checkbox is disabled.
   */
  CheckboxRootDataAttributes["disabled"] = "data-disabled";
  /**
   * Present when the checkbox is readonly.
   */
  CheckboxRootDataAttributes["readonly"] = "data-readonly";
  /**
   * Present when the checkbox is required.
   */
  CheckboxRootDataAttributes["required"] = "data-required";
  /**
   * Present when the checkbox is in valid state (when wrapped in Field.Root).
   */
  CheckboxRootDataAttributes["valid"] = "data-valid";
  /**
   * Present when the checkbox is in invalid state (when wrapped in Field.Root).
   */
  CheckboxRootDataAttributes["invalid"] = "data-invalid";
  /**
   * Present when the checkbox has been touched (when wrapped in Field.Root).
   */
  CheckboxRootDataAttributes["touched"] = "data-touched";
  /**
   * Present when the checkbox's value has changed (when wrapped in Field.Root).
   */
  CheckboxRootDataAttributes["dirty"] = "data-dirty";
  /**
   * Present when the checkbox is checked (when wrapped in Field.Root).
   */
  CheckboxRootDataAttributes["filled"] = "data-filled";
  /**
   * Present when the checkbox is focused (when wrapped in Field.Root).
   */
  CheckboxRootDataAttributes["focused"] = "data-focused";
  return CheckboxRootDataAttributes;
}({});