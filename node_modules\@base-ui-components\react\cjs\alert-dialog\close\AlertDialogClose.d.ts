import * as React from 'react';
import type { BaseUIComponentProps } from "../../utils/types.js";
/**
 * A button that closes the alert dialog.
 * Renders a `<button>` element.
 *
 * Documentation: [Base UI Alert Dialog](https://base-ui.com/react/components/alert-dialog)
 */
export declare const AlertDialogClose: React.ForwardRefExoticComponent<AlertDialogClose.Props & React.RefAttributes<HTMLButtonElement>>;
export declare namespace AlertDialogClose {
  interface Props extends BaseUIComponentProps<'button', State> {
    /**
     * Whether the component renders a native `<button>` element when replacing it
     * via the `render` prop.
     * Set to `false` if the rendered element is not a button (e.g. `<div>`).
     * @default true
     */
    nativeButton?: boolean;
  }
  interface State {
    /**
     * Whether the button is currently disabled.
     */
    disabled: boolean;
  }
}