"use strict";
'use client';

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.AlertDialogDescription = void 0;
var React = _interopRequireWildcard(require("react"));
var _AlertDialogRootContext = require("../root/AlertDialogRootContext");
var _useRenderElement = require("../../utils/useRenderElement");
var _useModernLayoutEffect = require("../../utils/useModernLayoutEffect");
var _useBaseUiId = require("../../utils/useBaseUiId");
/**
 * A paragraph with additional information about the alert dialog.
 * Renders a `<p>` element.
 *
 * Documentation: [Base UI Alert Dialog](https://base-ui.com/react/components/alert-dialog)
 */
const AlertDialogDescription = exports.AlertDialogDescription = /*#__PURE__*/React.forwardRef(function AlertDialogDescription(componentProps, forwardedRef) {
  const {
    render,
    className,
    id: idProp,
    ...elementProps
  } = componentProps;
  const {
    setDescriptionElementId
  } = (0, _AlertDialogRootContext.useAlertDialogRootContext)();
  const id = (0, _useBaseUiId.useBaseUiId)(idProp);
  (0, _useModernLayoutEffect.useModernLayoutEffect)(() => {
    setDescriptionElementId(id);
    return () => {
      setDescriptionElementId(undefined);
    };
  }, [id, setDescriptionElementId]);
  return (0, _useRenderElement.useRenderElement)('p', componentProps, {
    ref: forwardedRef,
    props: [{
      id
    }, elementProps]
  });
});
if (process.env.NODE_ENV !== "production") AlertDialogDescription.displayName = "AlertDialogDescription";