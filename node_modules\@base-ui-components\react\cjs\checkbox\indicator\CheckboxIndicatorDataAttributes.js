"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.CheckboxIndicatorDataAttributes = void 0;
var _styleHookMapping = require("../../utils/styleHookMapping");
let CheckboxIndicatorDataAttributes = exports.CheckboxIndicatorDataAttributes = function (CheckboxIndicatorDataAttributes) {
  /**
   * Present when the checkbox is checked.
   */
  CheckboxIndicatorDataAttributes["checked"] = "data-checked";
  /**
   * Present when the checkbox is not checked.
   */
  CheckboxIndicatorDataAttributes["unchecked"] = "data-unchecked";
  /**
   * Present when the checkbox is disabled.
   */
  CheckboxIndicatorDataAttributes["disabled"] = "data-disabled";
  /**
   * Present when the checkbox is readonly.
   */
  CheckboxIndicatorDataAttributes["readonly"] = "data-readonly";
  /**
   * Present when the checkbox is required.
   */
  CheckboxIndicatorDataAttributes["required"] = "data-required";
  /**
   * Present when the checkbox indicator is animating in.
   */
  CheckboxIndicatorDataAttributes[CheckboxIndicatorDataAttributes["startingStyle"] = _styleHookMapping.TransitionStatusDataAttributes.startingStyle] = "startingStyle";
  /**
   * Present when the checkbox indicator is animating out.
   */
  CheckboxIndicatorDataAttributes[CheckboxIndicatorDataAttributes["endingStyle"] = _styleHookMapping.TransitionStatusDataAttributes.endingStyle] = "endingStyle";
  /**
   * Present when the checkbox is in valid state (when wrapped in Field.Root).
   */
  CheckboxIndicatorDataAttributes["valid"] = "data-valid";
  /**
   * Present when the checkbox is in invalid state (when wrapped in Field.Root).
   */
  CheckboxIndicatorDataAttributes["invalid"] = "data-invalid";
  /**
   * Present when the checkbox has been touched (when wrapped in Field.Root).
   */
  CheckboxIndicatorDataAttributes["touched"] = "data-touched";
  /**
   * Present when the checkbox's value has changed (when wrapped in Field.Root).
   */
  CheckboxIndicatorDataAttributes["dirty"] = "data-dirty";
  /**
   * Present when the checkbox is checked (when wrapped in Field.Root).
   */
  CheckboxIndicatorDataAttributes["filled"] = "data-filled";
  /**
   * Present when the checkbox is focused (when wrapped in Field.Root).
   */
  CheckboxIndicatorDataAttributes["focused"] = "data-focused";
  return CheckboxIndicatorDataAttributes;
}({});