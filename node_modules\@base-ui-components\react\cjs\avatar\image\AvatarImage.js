"use strict";
'use client';

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.AvatarImage = void 0;
var React = _interopRequireWildcard(require("react"));
var _useRenderElement = require("../../utils/useRenderElement");
var _useEventCallback = require("../../utils/useEventCallback");
var _useModernLayoutEffect = require("../../utils/useModernLayoutEffect");
var _AvatarRootContext = require("../root/AvatarRootContext");
var _styleHooks = require("../root/styleHooks");
var _useImageLoadingStatus = require("./useImageLoadingStatus");
/**
 * The image to be displayed in the avatar.
 * Renders an `<img>` element.
 *
 * Documentation: [Base UI Avatar](https://base-ui.com/react/components/avatar)
 */
const AvatarImage = exports.AvatarImage = /*#__PURE__*/React.forwardRef(function AvatarImage(componentProps, forwardedRef) {
  const {
    className,
    render,
    onLoadingStatusChange: onLoadingStatusChangeProp,
    referrerPolicy,
    crossOrigin,
    ...elementProps
  } = componentProps;
  const context = (0, _AvatarRootContext.useAvatarRootContext)();
  const imageLoadingStatus = (0, _useImageLoadingStatus.useImageLoadingStatus)(componentProps.src, {
    referrerPolicy,
    crossOrigin
  });
  const handleLoadingStatusChange = (0, _useEventCallback.useEventCallback)(status => {
    onLoadingStatusChangeProp?.(status);
    context.setImageLoadingStatus(status);
  });
  (0, _useModernLayoutEffect.useModernLayoutEffect)(() => {
    if (imageLoadingStatus !== 'idle') {
      handleLoadingStatusChange(imageLoadingStatus);
    }
  }, [imageLoadingStatus, handleLoadingStatusChange]);
  const state = React.useMemo(() => ({
    imageLoadingStatus
  }), [imageLoadingStatus]);
  const element = (0, _useRenderElement.useRenderElement)('img', componentProps, {
    state,
    ref: forwardedRef,
    props: elementProps,
    customStyleHookMapping: _styleHooks.avatarStyleHookMapping,
    enabled: imageLoadingStatus === 'loaded'
  });
  return element;
});
if (process.env.NODE_ENV !== "production") AvatarImage.displayName = "AvatarImage";