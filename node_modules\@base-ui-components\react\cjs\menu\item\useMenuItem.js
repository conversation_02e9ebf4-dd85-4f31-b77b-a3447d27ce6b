"use strict";
'use client';

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.REGULAR_ITEM = void 0;
exports.useMenuItem = useMenuItem;
var React = _interopRequireWildcard(require("react"));
var _useButton = require("../../use-button");
var _mergeProps = require("../../merge-props");
var _utils = require("../../utils");
const REGULAR_ITEM = exports.REGULAR_ITEM = {
  type: 'regular-item'
};
function useMenuItem(params) {
  const {
    closeOnClick,
    disabled = false,
    highlighted,
    id,
    menuEvents,
    allowMouseUpTriggerRef,
    typingRef,
    nativeButton,
    itemMetadata
  } = params;
  const itemRef = React.useRef(null);
  const {
    getButtonProps,
    buttonRef
  } = (0, _useButton.useButton)({
    disabled,
    focusableWhenDisabled: true,
    native: nativeButton
  });
  const getItemProps = React.useCallback(externalProps => {
    return (0, _mergeProps.mergeProps)({
      id,
      role: 'menuitem',
      tabIndex: highlighted ? 0 : -1,
      onMouseEnter() {
        if (itemMetadata.type !== 'submenu-trigger') {
          return;
        }
        itemMetadata.setActive();
      },
      onKeyUp: event => {
        if (event.key === ' ' && typingRef.current) {
          event.preventBaseUIHandler();
        }
      },
      onClick: event => {
        if (closeOnClick) {
          menuEvents.emit('close', {
            domEvent: event,
            reason: 'item-press'
          });
        }
      },
      onMouseUp: () => {
        if (itemRef.current && allowMouseUpTriggerRef.current) {
          // This fires whenever the user clicks on the trigger, moves the cursor, and releases it over the item.
          // We trigger the click and override the `closeOnClick` preference to always close the menu.
          if (itemMetadata.type === 'regular-item') {
            itemRef.current.click();
          }
        }
      }
    }, externalProps, getButtonProps);
  }, [id, highlighted, getButtonProps, typingRef, closeOnClick, menuEvents, allowMouseUpTriggerRef, itemMetadata]);
  const mergedRef = (0, _utils.useForkRef)(itemRef, buttonRef);
  return React.useMemo(() => ({
    getItemProps,
    itemRef: mergedRef
  }), [getItemProps, mergedRef]);
}