"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.DialogBackdropDataAttributes = void 0;
var _popupStateMapping = require("../../utils/popupStateMapping");
let DialogBackdropDataAttributes = exports.DialogBackdropDataAttributes = function (DialogBackdropDataAttributes) {
  /**
   * Present when the dialog is open.
   */
  DialogBackdropDataAttributes[DialogBackdropDataAttributes["open"] = _popupStateMapping.CommonPopupDataAttributes.open] = "open";
  /**
   * Present when the dialog is closed.
   */
  DialogBackdropDataAttributes[DialogBackdropDataAttributes["closed"] = _popupStateMapping.CommonPopupDataAttributes.closed] = "closed";
  /**
   * Present when the dialog is animating in.
   */
  DialogBackdropDataAttributes[DialogBackdropDataAttributes["startingStyle"] = _popupStateMapping.CommonPopupDataAttributes.startingStyle] = "startingStyle";
  /**
   * Present when the dialog is animating out.
   */
  DialogBackdropDataAttributes[DialogBackdropDataAttributes["endingStyle"] = _popupStateMapping.CommonPopupDataAttributes.endingStyle] = "endingStyle";
  return DialogBackdropDataAttributes;
}({});