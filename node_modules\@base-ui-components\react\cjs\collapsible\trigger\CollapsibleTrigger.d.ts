import * as React from 'react';
import { BaseUIComponentProps } from "../../utils/types.js";
import { CollapsibleRoot } from "../root/CollapsibleRoot.js";
/**
 * A button that opens and closes the collapsible panel.
 * Renders a `<button>` element.
 *
 * Documentation: [Base UI Collapsible](https://base-ui.com/react/components/collapsible)
 */
export declare const CollapsibleTrigger: React.ForwardRefExoticComponent<CollapsibleTrigger.Props & React.RefAttributes<HTMLButtonElement>>;
export declare namespace CollapsibleTrigger {
  interface Props extends BaseUIComponentProps<'button', CollapsibleRoot.State> {
    /**
     * Whether the component renders a native `<button>` element when replacing it
     * via the `render` prop.
     * Set to `false` if the rendered element is not a button (e.g. `<div>`).
     * @default true
     */
    nativeButton?: boolean;
  }
}