"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.DialogPopupDataAttributes = void 0;
var _popupStateMapping = require("../../utils/popupStateMapping");
let DialogPopupDataAttributes = exports.DialogPopupDataAttributes = function (DialogPopupDataAttributes) {
  /**
   * Present when the dialog is open.
   */
  DialogPopupDataAttributes[DialogPopupDataAttributes["open"] = _popupStateMapping.CommonPopupDataAttributes.open] = "open";
  /**
   * Present when the dialog is closed.
   */
  DialogPopupDataAttributes[DialogPopupDataAttributes["closed"] = _popupStateMapping.CommonPopupDataAttributes.closed] = "closed";
  /**
   * Present when the dialog is animating in.
   */
  DialogPopupDataAttributes[DialogPopupDataAttributes["startingStyle"] = _popupStateMapping.CommonPopupDataAttributes.startingStyle] = "startingStyle";
  /**
   * Present when the dialog is animating out.
   */
  DialogPopupDataAttributes[DialogPopupDataAttributes["endingStyle"] = _popupStateMapping.CommonPopupDataAttributes.endingStyle] = "endingStyle";
  /**
   * Present when the dialog is nested within another dialog.
   */
  DialogPopupDataAttributes["nested"] = "data-nested";
  /**
   * Present when the dialog has other open dialogs nested within it.
   */
  DialogPopupDataAttributes["nestedDialogOpen"] = "data-nested-dialog-open";
  return DialogPopupDataAttributes;
}({});