"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.FieldRootDataAttributes = void 0;
let FieldRootDataAttributes = exports.FieldRootDataAttributes = /*#__PURE__*/function (FieldRootDataAttributes) {
  /**
   * Present when the field is disabled.
   */
  FieldRootDataAttributes["disabled"] = "data-disabled";
  /**
   * Present when the field has been touched.
   */
  FieldRootDataAttributes["touched"] = "data-touched";
  /**
   * Present when the field's value has changed.
   */
  FieldRootDataAttributes["dirty"] = "data-dirty";
  /**
   * Present when the field is valid.
   */
  FieldRootDataAttributes["valid"] = "data-valid";
  /**
   * Present when the field is invalid.
   */
  FieldRootDataAttributes["invalid"] = "data-invalid";
  /**
   * Present when the field is filled.
   */
  FieldRootDataAttributes["filled"] = "data-filled";
  /**
   * Present when the field control is focused.
   */
  FieldRootDataAttributes["focused"] = "data-focused";
  return FieldRootDataAttributes;
}({});