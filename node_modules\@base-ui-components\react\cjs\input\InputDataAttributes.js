"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.InputDataAttributes = void 0;
let InputDataAttributes = exports.InputDataAttributes = /*#__PURE__*/function (InputDataAttributes) {
  /**
   * Present when the input is disabled.
   */
  InputDataAttributes["disabled"] = "data-disabled";
  /**
   * Present when the input is in valid state.
   */
  InputDataAttributes["valid"] = "data-valid";
  /**
   * Present when the input is in invalid state.
   */
  InputDataAttributes["invalid"] = "data-invalid";
  /**
   * Present when the input has been touched.
   */
  InputDataAttributes["touched"] = "data-touched";
  /**
   * Present when the input's value has changed.
   */
  InputDataAttributes["dirty"] = "data-dirty";
  /**
   * Present when the input is filled.
   */
  InputDataAttributes["filled"] = "data-filled";
  /**
   * Present when the input is focused.
   */
  InputDataAttributes["focused"] = "data-focused";
  return InputDataAttributes;
}({});