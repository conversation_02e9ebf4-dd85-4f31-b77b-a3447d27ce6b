"use strict";
'use client';

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useDialogPopup = useDialogPopup;
var React = _interopRequireWildcard(require("react"));
var _useForkRef = require("../../utils/useForkRef");
var _composite = require("../../composite/composite");
function useDialogPopup(parameters) {
  const {
    descriptionElementId,
    initialFocus,
    modal,
    mounted,
    openMethod,
    ref,
    setPopupElement,
    titleElementId
  } = parameters;
  const popupRef = React.useRef(null);
  const handleRef = (0, _useForkRef.useForkRef)(ref, popupRef, setPopupElement);

  // Default initial focus logic:
  // If opened by touch, focus the popup element to prevent the virtual keyboard from opening
  // (this is required for Android specifically as iOS handles this automatically).
  const defaultInitialFocus = React.useCallback(interactionType => {
    if (interactionType === 'touch') {
      return popupRef;
    }
    return 0;
  }, []);
  const resolvedInitialFocus = React.useMemo(() => {
    if (initialFocus == null) {
      return defaultInitialFocus(openMethod ?? '');
    }
    if (typeof initialFocus === 'function') {
      return initialFocus(openMethod ?? '');
    }
    return initialFocus;
  }, [defaultInitialFocus, initialFocus, openMethod]);
  const popupProps = {
    'aria-labelledby': titleElementId ?? undefined,
    'aria-describedby': descriptionElementId ?? undefined,
    'aria-modal': mounted && modal === true ? true : undefined,
    role: 'dialog',
    tabIndex: -1,
    ref: handleRef,
    hidden: !mounted,
    onKeyDown(event) {
      if (_composite.COMPOSITE_KEYS.has(event.key)) {
        event.stopPropagation();
      }
    }
  };
  return {
    popupProps,
    resolvedInitialFocus
  };
}