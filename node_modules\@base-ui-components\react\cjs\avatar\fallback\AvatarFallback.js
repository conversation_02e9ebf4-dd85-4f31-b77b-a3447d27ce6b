"use strict";
'use client';

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.AvatarFallback = void 0;
var React = _interopRequireWildcard(require("react"));
var _useRenderElement = require("../../utils/useRenderElement");
var _useTimeout = require("../../utils/useTimeout");
var _AvatarRootContext = require("../root/AvatarRootContext");
var _styleHooks = require("../root/styleHooks");
/**
 * Rendered when the image fails to load or when no image is provided.
 * Renders a `<span>` element.
 *
 * Documentation: [Base UI Avatar](https://base-ui.com/react/components/avatar)
 */
const AvatarFallback = exports.AvatarFallback = /*#__PURE__*/React.forwardRef(function AvatarFallback(componentProps, forwardedRef) {
  const {
    className,
    render,
    delay,
    ...elementProps
  } = componentProps;
  const {
    imageLoadingStatus
  } = (0, _AvatarRootContext.useAvatarRootContext)();
  const [delayPassed, setDelayPassed] = React.useState(delay === undefined);
  const timeout = (0, _useTimeout.useTimeout)();
  React.useEffect(() => {
    if (delay !== undefined) {
      timeout.start(delay, () => setDelayPassed(true));
    }
    return timeout.clear;
  }, [timeout, delay]);
  const state = React.useMemo(() => ({
    imageLoadingStatus
  }), [imageLoadingStatus]);
  const element = (0, _useRenderElement.useRenderElement)('span', componentProps, {
    state,
    ref: forwardedRef,
    props: elementProps,
    customStyleHookMapping: _styleHooks.avatarStyleHookMapping,
    enabled: imageLoadingStatus !== 'loaded' && delayPassed
  });
  return element;
});
if (process.env.NODE_ENV !== "production") AvatarFallback.displayName = "AvatarFallback";