"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.AlertDialogPopupDataAttributes = void 0;
var _popupStateMapping = require("../../utils/popupStateMapping");
let AlertDialogPopupDataAttributes = exports.AlertDialogPopupDataAttributes = function (AlertDialogPopupDataAttributes) {
  /**
   * Present when the dialog is open.
   */
  AlertDialogPopupDataAttributes[AlertDialogPopupDataAttributes["open"] = _popupStateMapping.CommonPopupDataAttributes.open] = "open";
  /**
   * Present when the dialog is closed.
   */
  AlertDialogPopupDataAttributes[AlertDialogPopupDataAttributes["closed"] = _popupStateMapping.CommonPopupDataAttributes.closed] = "closed";
  /**
   * Present when the dialog is animating in.
   */
  AlertDialogPopupDataAttributes[AlertDialogPopupDataAttributes["startingStyle"] = _popupStateMapping.CommonPopupDataAttributes.startingStyle] = "startingStyle";
  /**
   * Present when the dialog is animating out.
   */
  AlertDialogPopupDataAttributes[AlertDialogPopupDataAttributes["endingStyle"] = _popupStateMapping.CommonPopupDataAttributes.endingStyle] = "endingStyle";
  /**
   * Present when the dialog is nested within another dialog.
   */
  AlertDialogPopupDataAttributes["nested"] = "data-nested";
  /**
   * Present when the dialog has other open dialogs nested within it.
   */
  AlertDialogPopupDataAttributes["nestedDialogOpen"] = "data-nested-dialog-open";
  return AlertDialogPopupDataAttributes;
}({});