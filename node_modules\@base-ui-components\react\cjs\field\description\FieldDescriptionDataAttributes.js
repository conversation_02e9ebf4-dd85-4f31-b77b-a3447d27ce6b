"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.FieldDescriptionDataAttributes = void 0;
let FieldDescriptionDataAttributes = exports.FieldDescriptionDataAttributes = /*#__PURE__*/function (FieldDescriptionDataAttributes) {
  /**
   * Present when the field is disabled.
   */
  FieldDescriptionDataAttributes["disabled"] = "data-disabled";
  /**
   * Present when the field is in valid state.
   */
  FieldDescriptionDataAttributes["valid"] = "data-valid";
  /**
   * Present when the field is in invalid state.
   */
  FieldDescriptionDataAttributes["invalid"] = "data-invalid";
  /**
   * Present when the field has been touched.
   */
  FieldDescriptionDataAttributes["touched"] = "data-touched";
  /**
   * Present when the field's value has changed.
   */
  FieldDescriptionDataAttributes["dirty"] = "data-dirty";
  /**
   * Present when the field is filled.
   */
  FieldDescriptionDataAttributes["filled"] = "data-filled";
  /**
   * Present when the field control is focused.
   */
  FieldDescriptionDataAttributes["focused"] = "data-focused";
  return FieldDescriptionDataAttributes;
}({});