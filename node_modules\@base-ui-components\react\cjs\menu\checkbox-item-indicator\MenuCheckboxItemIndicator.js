"use strict";
'use client';

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MenuCheckboxItemIndicator = void 0;
var React = _interopRequireWildcard(require("react"));
var _MenuCheckboxItemContext = require("../checkbox-item/MenuCheckboxItemContext");
var _useRenderElement = require("../../utils/useRenderElement");
var _styleHookMapping = require("../utils/styleHookMapping");
var _useTransitionStatus = require("../../utils/useTransitionStatus");
var _useOpenChangeComplete = require("../../utils/useOpenChangeComplete");
/**
 * Indicates whether the checkbox item is ticked.
 * Renders a `<div>` element.
 *
 * Documentation: [Base UI Menu](https://base-ui.com/react/components/menu)
 */
const MenuCheckboxItemIndicator = exports.MenuCheckboxItemIndicator = /*#__PURE__*/React.forwardRef(function MenuCheckboxItemIndicator(componentProps, forwardedRef) {
  const {
    render,
    className,
    keepMounted = false,
    ...elementProps
  } = componentProps;
  const item = (0, _MenuCheckboxItemContext.useMenuCheckboxItemContext)();
  const indicatorRef = React.useRef(null);
  const {
    transitionStatus,
    setMounted
  } = (0, _useTransitionStatus.useTransitionStatus)(item.checked);
  (0, _useOpenChangeComplete.useOpenChangeComplete)({
    open: item.checked,
    ref: indicatorRef,
    onComplete() {
      if (!item.checked) {
        setMounted(false);
      }
    }
  });
  const state = React.useMemo(() => ({
    checked: item.checked,
    disabled: item.disabled,
    highlighted: item.highlighted,
    transitionStatus
  }), [item.checked, item.disabled, item.highlighted, transitionStatus]);
  const element = (0, _useRenderElement.useRenderElement)('span', componentProps, {
    state,
    ref: [forwardedRef, indicatorRef],
    customStyleHookMapping: _styleHookMapping.itemMapping,
    props: {
      'aria-hidden': true,
      ...elementProps
    },
    enabled: keepMounted || item.checked
  });
  return element;
});
if (process.env.NODE_ENV !== "production") MenuCheckboxItemIndicator.displayName = "MenuCheckboxItemIndicator";