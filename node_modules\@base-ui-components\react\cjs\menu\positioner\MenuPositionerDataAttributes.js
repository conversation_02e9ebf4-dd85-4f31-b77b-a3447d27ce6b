"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MenuPositionerDataAttributes = void 0;
var _popupStateMapping = require("../../utils/popupStateMapping");
let MenuPositionerDataAttributes = exports.MenuPositionerDataAttributes = function (MenuPositionerDataAttributes) {
  /**
   * Present when the menu popup is open.
   */
  MenuPositionerDataAttributes[MenuPositionerDataAttributes["open"] = _popupStateMapping.CommonPopupDataAttributes.open] = "open";
  /**
   * Present when the menu popup is closed.
   */
  MenuPositionerDataAttributes[MenuPositionerDataAttributes["closed"] = _popupStateMapping.CommonPopupDataAttributes.closed] = "closed";
  /**
   * Present when the anchor is hidden.
   */
  MenuPositionerDataAttributes[MenuPositionerDataAttributes["anchorHidden"] = _popupStateMapping.CommonPopupDataAttributes.anchorHidden] = "anchorHidden";
  /**
   * Indicates which side the popup is positioned relative to the trigger.
   * @type {'top' | 'bottom' | 'left' | 'right' | 'inline-end' | 'inline-start'}
   */
  MenuPositionerDataAttributes["side"] = "data-side";
  /**
   * Indicates how the popup is aligned relative to specified side.
   * @type {'start' | 'center' | 'end'}
   */
  MenuPositionerDataAttributes["align"] = "data-align";
  return MenuPositionerDataAttributes;
}({});