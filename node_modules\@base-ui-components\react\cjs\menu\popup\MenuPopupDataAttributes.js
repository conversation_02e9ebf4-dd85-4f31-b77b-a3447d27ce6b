"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MenuPopupDataAttributes = void 0;
var _popupStateMapping = require("../../utils/popupStateMapping");
let MenuPopupDataAttributes = exports.MenuPopupDataAttributes = function (MenuPopupDataAttributes) {
  /**
   * Present when the menu is open.
   */
  MenuPopupDataAttributes[MenuPopupDataAttributes["open"] = _popupStateMapping.CommonPopupDataAttributes.open] = "open";
  /**
   * Present when the menu is closed.
   */
  MenuPopupDataAttributes[MenuPopupDataAttributes["closed"] = _popupStateMapping.CommonPopupDataAttributes.closed] = "closed";
  /**
   * Present when the menu is animating in.
   */
  MenuPopupDataAttributes[MenuPopupDataAttributes["startingStyle"] = _popupStateMapping.CommonPopupDataAttributes.startingStyle] = "startingStyle";
  /**
   * Present when the menu is animating out.
   */
  MenuPopupDataAttributes[MenuPopupDataAttributes["endingStyle"] = _popupStateMapping.CommonPopupDataAttributes.endingStyle] = "endingStyle";
  /**
   * Indicates which side the popup is positioned relative to the trigger.
   * @type {'top' | 'bottom' | 'left' | 'right' | 'inline-end' | 'inline-start'}
   */
  MenuPopupDataAttributes["side"] = "data-side";
  /**
   * Indicates how the popup is aligned relative to specified side.
   * @type {'start' | 'center' | 'end'}
   */
  MenuPopupDataAttributes["align"] = "data-align";
  /**
   * Present if animations should be instant.
   * @type {'click' | 'dismiss' | 'group'}
   */
  MenuPopupDataAttributes["instant"] = "data-instant";
  return MenuPopupDataAttributes;
}({});