"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MenuBackdropDataAttributes = void 0;
var _popupStateMapping = require("../../utils/popupStateMapping");
let MenuBackdropDataAttributes = exports.MenuBackdropDataAttributes = function (MenuBackdropDataAttributes) {
  /**
   * Present when the menu is open.
   */
  MenuBackdropDataAttributes[MenuBackdropDataAttributes["open"] = _popupStateMapping.CommonPopupDataAttributes.open] = "open";
  /**
   * Present when the menu is closed.
   */
  MenuBackdropDataAttributes[MenuBackdropDataAttributes["closed"] = _popupStateMapping.CommonPopupDataAttributes.closed] = "closed";
  /**
   * Present when the menu is animating in.
   */
  MenuBackdropDataAttributes[MenuBackdropDataAttributes["startingStyle"] = _popupStateMapping.CommonPopupDataAttributes.startingStyle] = "startingStyle";
  /**
   * Present when the menu is animating out.
   */
  MenuBackdropDataAttributes[MenuBackdropDataAttributes["endingStyle"] = _popupStateMapping.CommonPopupDataAttributes.endingStyle] = "endingStyle";
  return MenuBackdropDataAttributes;
}({});